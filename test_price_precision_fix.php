<?php
/**
 * 价格精度修复测试脚本
 * 测试价格计算和显示的精度问题是否已修复
 */

include('confing/common.php');

echo "=== 价格精度修复测试 ===\n\n";

// 测试数据
$test_cases = [
    ['price' => '0.975', 'addprice' => '0.4', 'expected' => '0.39'],
    ['price' => '7.5', 'addprice' => '0.2', 'expected' => '1.50'],
    ['price' => '4.5', 'addprice' => '0.3', 'expected' => '1.35'],
    ['price' => '1.5', 'addprice' => '0.6', 'expected' => '0.90'],
    ['price' => '3', 'addprice' => '0.25', 'expected' => '0.75'],
];

echo "1. 测试PHP价格计算精度修复:\n";
foreach ($test_cases as $i => $case) {
    $price = floatval($case['price']);
    $addprice = floatval($case['addprice']);
    
    // 修复前的计算方式（可能有精度问题）
    $old_result = $price * $addprice;
    
    // 修复后的计算方式
    $new_result = round($price * $addprice, 2);
    $formatted_result = number_format($new_result, 2, '.', '');
    
    echo sprintf(
        "测试 %d: %.3f × %.2f = %.8f (修复前) → %s (修复后) [期望: %s] %s\n",
        $i + 1,
        $price,
        $addprice,
        $old_result,
        $formatted_result,
        $case['expected'],
        ($formatted_result == $case['expected']) ? '✓' : '✗'
    );
}

echo "\n2. 测试数据库中的实际价格数据:\n";
try {
    // 获取一些实际的商品数据进行测试
    $sql = "SELECT cid, name, price FROM qingka_wangke_class WHERE status=1 LIMIT 5";
    $result = $DB->query($sql);
    
    if ($result) {
        while ($row = $DB->fetch($result)) {
            $price = floatval($row['price']);
            $test_addprice = 0.4; // 使用0.4作为测试费率
            
            // 修复前的计算
            $old_calc = $price * $test_addprice;
            
            // 修复后的计算
            $new_calc = round($price * $test_addprice, 2);
            $formatted = number_format($new_calc, 2, '.', '');
            
            echo sprintf(
                "商品 %s: 原价 %s × 0.4 = %.8f → %s %s\n",
                $row['cid'],
                $row['price'],
                $old_calc,
                $formatted,
                (strlen(sprintf('%.8f', $old_calc)) > 6) ? '(修复精度问题)' : ''
            );
        }
    }
} catch (Exception $e) {
    echo "数据库查询错误: " . $e->getMessage() . "\n";
}

echo "\n3. 测试JavaScript价格计算精度:\n";
echo "请在浏览器中打开价格页面 /index/price.php 测试前端价格计算\n";
echo "检查项目:\n";
echo "- 价格表中的价格是否显示为正确的2位小数\n";
echo "- 切换费率时价格计算是否精确\n";
echo "- 不再出现类似 0.40000058 的超长小数\n";

echo "\n4. 测试充值功能修复:\n";
echo "充值功能已添加事务保护，建议进行以下测试:\n";
echo "- 正常充值流程测试\n";
echo "- 余额不足时的错误处理\n";
echo "- 网络中断时的事务回滚\n";
echo "- 充值记录的完整性\n";

echo "\n=== 测试完成 ===\n";
echo "如果所有测试项都显示 ✓，说明价格精度问题已修复\n";
echo "建议在实际环境中进行充值功能测试以确保事务处理正常\n";
?>
