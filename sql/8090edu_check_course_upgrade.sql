-- 8090教育查课功能优化升级脚本
-- 添加check_course字段，实现查课/无查下单的区分
-- 解决查课失败仍能下单的问题

-- 1. 为qingka_wangke_class表添加check_course字段
-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND COLUMN_NAME = 'check_course'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD COLUMN check_course VARCHAR(20) DEFAULT "未知" COMMENT "查课支持状态：支持/不支持/未知"', 
    'SELECT "Column check_course already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 为qingka_wangke_class表添加exam_support字段
-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND COLUMN_NAME = 'exam_support'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD COLUMN exam_support VARCHAR(20) DEFAULT "未知" COMMENT "考试支持状态：支持/不支持/未知"', 
    'SELECT "Column exam_support already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为qingka_wangke_class表添加site_status字段
-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND COLUMN_NAME = 'site_status'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD COLUMN site_status VARCHAR(20) DEFAULT "正常" COMMENT "网站状态：正常/维护中/异常"', 
    'SELECT "Column site_status already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 从现有的content字段中提取check_course信息并更新
-- 获取8090教育的货源ID
SET @hid_8090 = (
    SELECT hid 
    FROM qingka_wangke_huoyuan 
    WHERE pt = '8090edu' OR name LIKE '%8090%'
    LIMIT 1
);

-- 更新8090教育项目的check_course字段
UPDATE qingka_wangke_class 
SET check_course = CASE 
    WHEN content LIKE '%项目查课: 支持%' THEN '支持'
    WHEN content LIKE '%项目查课: 不支持%' THEN '不支持'
    ELSE '未知'
END,
exam_support = CASE 
    WHEN content LIKE '%项目考试: 支持%' THEN '支持'
    WHEN content LIKE '%项目考试: 不支持%' THEN '不支持'
    ELSE '未知'
END,
site_status = CASE 
    WHEN content LIKE '%项目状态: 正常%' THEN '正常'
    WHEN content LIKE '%项目状态: 维护中%' THEN '维护中'
    WHEN content LIKE '%项目状态: 异常%' THEN '异常'
    ELSE '正常'
END
WHERE docking = @hid_8090 AND @hid_8090 IS NOT NULL;

-- 5. 添加索引优化查询性能
-- 检查并添加check_course字段索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_check_course'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_check_course (check_course)', 
    'SELECT "Index idx_check_course already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 添加复合索引（docking + check_course）
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_docking_check_course'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_docking_check_course (docking, check_course)', 
    'SELECT "Index idx_docking_check_course already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 显示升级结果统计
SELECT 
    '8090教育查课功能升级完成' as message,
    NOW() as completion_time;

-- 显示8090教育项目查课支持统计
SELECT
    c.check_course,
    COUNT(*) as project_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM qingka_wangke_class c2
                              JOIN qingka_wangke_huoyuan h2 ON c2.docking = h2.hid
                              WHERE h2.pt = '8090edu'), 2) as percentage
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu'
GROUP BY c.check_course
ORDER BY project_count DESC;

-- 显示考试支持统计
SELECT
    c.exam_support,
    COUNT(*) as project_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu'
GROUP BY c.exam_support
ORDER BY project_count DESC;

-- 显示网站状态统计
SELECT
    c.site_status,
    COUNT(*) as project_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu'
GROUP BY c.site_status
ORDER BY project_count DESC;

-- 8. 验证数据完整性
-- 检查是否有未正确提取的记录
SELECT
    c.cid,
    c.name as project_name,
    c.check_course,
    c.exam_support,
    c.site_status,
    SUBSTRING(c.content, 1, 100) as content_preview
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu'
AND c.check_course = '未知'
LIMIT 5;

-- 显示完成信息
SELECT 
    CONCAT('升级完成！共处理 ', COUNT(*), ' 个8090教育项目') as summary
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu';
