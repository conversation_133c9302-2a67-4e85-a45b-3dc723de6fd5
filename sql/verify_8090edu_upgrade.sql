-- 8090教育查课功能升级验证脚本
-- 用于验证升级是否成功，检查数据完整性
-- 作者: AI Assistant
-- 创建时间: 2025-08-25

-- 1. 检查新增字段是否存在
SELECT 
    '字段检查' as check_type,
    COLUMN_NAME as field_name,
    DATA_TYPE as field_type,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'qingka_wangke_class' 
AND COLUMN_NAME IN ('check_course', 'exam_support', 'site_status')
ORDER BY COLUMN_NAME;

-- 2. 检查索引是否创建成功
SELECT 
    '索引检查' as check_type,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as is_unique
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'qingka_wangke_class' 
AND INDEX_NAME IN ('idx_check_course', 'idx_docking_check_course')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 3. 检查8090教育货源是否存在
SELECT 
    '货源检查' as check_type,
    h.hid,
    h.name as source_name,
    h.pt as platform,
    h.status as source_status,
    h.url as api_url
FROM qingka_wangke_huoyuan h
WHERE h.pt = '8090edu' OR h.name LIKE '%8090%'
ORDER BY h.hid;

-- 4. 统计8090教育项目总数
SELECT 
    '项目统计' as check_type,
    COUNT(*) as total_projects,
    SUM(CASE WHEN c.status = 1 THEN 1 ELSE 0 END) as active_projects,
    SUM(CASE WHEN c.status = 0 THEN 1 ELSE 0 END) as inactive_projects
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu';

-- 5. 查课支持状态分布统计
SELECT 
    '查课支持统计' as check_type,
    c.check_course,
    COUNT(*) as project_count,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM qingka_wangke_class c2 
        JOIN qingka_wangke_huoyuan h2 ON c2.docking = h2.hid 
        WHERE h2.pt = '8090edu' AND c2.status = 1
    ), 2) as percentage
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' AND c.status = 1
GROUP BY c.check_course
ORDER BY project_count DESC;

-- 6. 考试支持状态分布统计
SELECT 
    '考试支持统计' as check_type,
    c.exam_support,
    COUNT(*) as project_count,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM qingka_wangke_class c2 
        JOIN qingka_wangke_huoyuan h2 ON c2.docking = h2.hid 
        WHERE h2.pt = '8090edu' AND c2.status = 1
    ), 2) as percentage
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' AND c.status = 1
GROUP BY c.exam_support
ORDER BY project_count DESC;

-- 7. 网站状态分布统计
SELECT 
    '网站状态统计' as check_type,
    c.site_status,
    COUNT(*) as project_count,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM qingka_wangke_class c2 
        JOIN qingka_wangke_huoyuan h2 ON c2.docking = h2.hid 
        WHERE h2.pt = '8090edu' AND c2.status = 1
    ), 2) as percentage
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' AND c.status = 1
GROUP BY c.site_status
ORDER BY project_count DESC;

-- 8. 检查未正确提取的记录（查课状态为未知的项目）
SELECT 
    '数据完整性检查' as check_type,
    c.cid,
    c.name as project_name,
    c.noun as project_id,
    c.check_course,
    c.exam_support,
    c.site_status,
    SUBSTRING(c.content, 1, 150) as content_preview
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND c.check_course = '未知'
ORDER BY c.cid
LIMIT 10;

-- 9. 检查content字段中包含查课信息但字段未更新的记录
SELECT 
    '数据同步检查' as check_type,
    c.cid,
    c.name as project_name,
    c.noun as project_id,
    c.check_course as current_check_course,
    CASE 
        WHEN c.content LIKE '%项目查课: 支持%' THEN '支持'
        WHEN c.content LIKE '%项目查课: 不支持%' THEN '不支持'
        ELSE '未知'
    END as content_check_course,
    SUBSTRING(c.content, 1, 100) as content_preview
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND (
    (c.content LIKE '%项目查课: 支持%' AND c.check_course != '支持') OR
    (c.content LIKE '%项目查课: 不支持%' AND c.check_course != '不支持')
)
ORDER BY c.cid
LIMIT 10;

-- 10. 显示支持查课的项目样例
SELECT 
    '支持查课项目样例' as check_type,
    c.cid,
    c.name as project_name,
    c.noun as project_id,
    c.check_course,
    c.exam_support,
    c.site_status,
    c.price
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND c.check_course = '支持'
ORDER BY c.cid
LIMIT 5;

-- 11. 显示不支持查课的项目样例
SELECT 
    '不支持查课项目样例' as check_type,
    c.cid,
    c.name as project_name,
    c.noun as project_id,
    c.check_course,
    c.exam_support,
    c.site_status,
    c.price
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND c.check_course = '不支持'
ORDER BY c.cid
LIMIT 5;

-- 12. 检查可能的数据异常
SELECT 
    '数据异常检查' as check_type,
    '空字段检查' as issue_type,
    COUNT(*) as issue_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND (c.check_course IS NULL OR c.check_course = '' OR 
     c.exam_support IS NULL OR c.exam_support = '' OR
     c.site_status IS NULL OR c.site_status = '')

UNION ALL

SELECT 
    '数据异常检查' as check_type,
    '无效状态值检查' as issue_type,
    COUNT(*) as issue_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' 
AND c.status = 1
AND (c.check_course NOT IN ('支持', '不支持', '未知') OR
     c.exam_support NOT IN ('支持', '不支持', '未知') OR
     c.site_status NOT IN ('正常', '维护中', '异常'));

-- 13. 升级完成总结
SELECT 
    '升级总结' as summary_type,
    CONCAT('8090教育查课功能升级验证完成 - ', NOW()) as message,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1) as total_projects,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1 AND c.check_course = '支持') as support_check_projects,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1 AND c.check_course = '不支持') as no_check_projects,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1 AND c.check_course = '未知') as unknown_projects;
