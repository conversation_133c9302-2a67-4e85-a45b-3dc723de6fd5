-- 修复8090教育升级脚本中的字段冲突问题
-- 解决 "Column 'name' in field list is ambiguous" 错误
-- 作者: AI Assistant
-- 创建时间: 2025-08-25

-- 如果之前的升级脚本执行失败，可以使用这个脚本进行修复

-- 1. 检查字段是否已存在，如果不存在则添加
-- 添加check_course字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'qingka_wangke_class' 
     AND COLUMN_NAME = 'check_course') = 0,
    'ALTER TABLE qingka_wangke_class ADD COLUMN check_course VARCHAR(20) DEFAULT "未知" COMMENT "查课支持状态：支持/不支持/未知"',
    'SELECT "check_course字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加exam_support字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'qingka_wangke_class' 
     AND COLUMN_NAME = 'exam_support') = 0,
    'ALTER TABLE qingka_wangke_class ADD COLUMN exam_support VARCHAR(20) DEFAULT "未知" COMMENT "考试支持状态：支持/不支持/未知"',
    'SELECT "exam_support字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加site_status字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'qingka_wangke_class' 
     AND COLUMN_NAME = 'site_status') = 0,
    'ALTER TABLE qingka_wangke_class ADD COLUMN site_status VARCHAR(20) DEFAULT "正常" COMMENT "网站状态：正常/维护中/异常"',
    'SELECT "site_status字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 更新8090教育项目的字段值（修复字段冲突的版本）
UPDATE qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
SET 
    c.check_course = CASE 
        WHEN c.content LIKE '%项目查课: 支持%' THEN '支持'
        WHEN c.content LIKE '%项目查课: 不支持%' THEN '不支持'
        ELSE '未知'
    END,
    c.exam_support = CASE 
        WHEN c.content LIKE '%项目考试: 支持%' THEN '支持'
        WHEN c.content LIKE '%项目考试: 不支持%' THEN '不支持'
        ELSE '未知'
    END,
    c.site_status = CASE 
        WHEN c.content LIKE '%项目状态: 正常%' THEN '正常'
        WHEN c.content LIKE '%项目状态: 维护中%' THEN '维护中'
        WHEN c.content LIKE '%项目状态: 异常%' THEN '异常'
        ELSE '正常'
    END
WHERE h.pt = '8090edu';

-- 3. 添加索引（如果不存在）
-- 添加check_course索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'qingka_wangke_class' 
     AND INDEX_NAME = 'idx_check_course') = 0,
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_check_course (check_course)',
    'SELECT "idx_check_course索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加复合索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'qingka_wangke_class' 
     AND INDEX_NAME = 'idx_docking_check_course') = 0,
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_docking_check_course (docking, check_course)',
    'SELECT "idx_docking_check_course索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 验证修复结果（修复字段冲突的版本）
SELECT 
    '修复验证' as check_type,
    '查课支持统计' as detail_type,
    c.check_course,
    COUNT(*) as project_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' AND c.status = 1
GROUP BY c.check_course
ORDER BY project_count DESC;

-- 显示修复完成信息
SELECT 
    '修复完成' as status,
    CONCAT('8090教育字段冲突修复完成 - ', NOW()) as message,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1) as total_projects,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1 AND c.check_course = '支持') as support_check_count,
    (SELECT COUNT(*) FROM qingka_wangke_class c 
     JOIN qingka_wangke_huoyuan h ON c.docking = h.hid 
     WHERE h.pt = '8090edu' AND c.status = 1 AND c.check_course = '不支持') as no_check_count;
