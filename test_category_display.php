<?php
/**
 * 测试分类显示功能
 * 用于验证29系统分类名称显示是否正常
 */

// 引入公共配置文件
include('confing/common.php');

// 模拟不同类型的货源数据
$test_sources = [
    [
        'hid' => 1,
        'name' => '29系统测试',
        'pt' => '29',
        'url' => 'http://test29.com',
        'user' => 'test',
        'pass' => 'test'
    ],
    [
        'hid' => 2,
        'name' => 'yyy教育',
        'pt' => 'yyy',
        'url' => 'http://yyy.com',
        'user' => 'test',
        'pass' => 'test'
    ],
    [
        'hid' => 3,
        'name' => '8090教育',
        'pt' => '8090edu',
        'url' => 'http://8090.com',
        'user' => 'test',
        'pass' => 'test'
    ]
];

echo "<h1>分类显示功能测试</h1>";
echo "<h2>测试不同平台的分类处理逻辑</h2>";

foreach ($test_sources as $source) {
    echo "<h3>平台: {$source['name']} (类型: {$source['pt']})</h3>";
    
    // 检查是否为29系统
    $is_29_system = false;
    if (strpos($source["pt"], "29") !== false || strpos($source["name"], "29") !== false) {
        $is_29_system = true;
    }
    
    echo "<p>是否为29系统: " . ($is_29_system ? "是" : "否") . "</p>";
    
    // 模拟商品数据
    $mock_products = [
        [
            'cid' => 1,
            'name' => '测试商品1',
            'fenlei' => 1,
            'fenleiname' => '继续教育',
            'price' => 100
        ],
        [
            'cid' => 2,
            'name' => '测试商品2',
            'fenlei' => 2,
            'fenleiname' => '职业培训',
            'price' => 200
        ]
    ];
    
    // 模拟分类数据
    $mock_categories = [
        1 => '继续教育',
        2 => '职业培训'
    ];
    
    echo "<h4>商品列表:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>商品ID</th><th>商品名称</th><th>分类ID</th><th>分类名称显示</th><th>价格</th></tr>";
    
    foreach ($mock_products as $product) {
        $fenlei_display = "";
        
        if ($is_29_system && isset($product['fenlei'])) {
            $fenlei_id = $product['fenlei'];
            if (isset($mock_categories[$fenlei_id])) {
                $fenlei_display = $mock_categories[$fenlei_id];
            } else if (isset($product['fenleiname'])) {
                $fenlei_display = $product['fenleiname'];
            } else {
                $fenlei_display = "分类ID: " . $fenlei_id;
            }
        } else {
            $fenlei_display = "不显示分类";
        }
        
        echo "<tr>";
        echo "<td>{$product['cid']}</td>";
        echo "<td>{$product['name']}</td>";
        echo "<td>{$product['fenlei']}</td>";
        echo "<td>{$fenlei_display}</td>";
        echo "<td>{$product['price']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<hr>";
}

echo "<h2>总结</h2>";
echo "<ul>";
echo "<li>29系统：显示分类名称（如：继续教育、职业培训）</li>";
echo "<li>yyy教育：不显示分类信息</li>";
echo "<li>8090教育：不显示分类信息</li>";
echo "<li>易教育：不显示分类信息</li>";
echo "</ul>";

echo "<p><a href='index/yjdj.php'>返回综合对接页面</a></p>";
?>
