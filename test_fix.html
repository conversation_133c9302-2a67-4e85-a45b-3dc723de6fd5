<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试</title>
    <link rel="stylesheet" href="assets/css/select-optimization.css">
    <link rel="stylesheet" href="assets/css/scrollbar-fix.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-option {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 10px;
            background: white;
        }
        .test-option:hover {
            background: #f8f9fa;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>修复验证测试</h1>
        
        <div class="status">
            <strong>✅ 修复状态：</strong><br>
            1. 价格标签应该显示在前面<br>
            2. 项目名称应该显示在后面<br>
            3. 长项目名称应该有滚动条<br>
            4. 两个元素都应该可见
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">50积分</span>
                <span class="project-name">这是一个非常非常非常长的项目名称，用来测试是否能同时显示价格和名称</span>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">100积分</span>
                <span class="project-name">短名称</span>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">200积分</span>
                <span class="project-name">中等长度的项目名称测试案例</span>
            </div>
        </div>
    </div>

    <script>
        // 检查元素是否正确显示
        setTimeout(() => {
            const options = document.querySelectorAll('.test-option');
            options.forEach((option, index) => {
                const price = option.querySelector('.project-price');
                const name = option.querySelector('.project-name');
                
                console.log(`选项 ${index + 1}:`);
                console.log('- 价格显示:', price ? '✅' : '❌');
                console.log('- 名称显示:', name ? '✅' : '❌');
                console.log('- 价格文本:', price ? price.textContent : '无');
                console.log('- 名称文本:', name ? name.textContent : '无');
                console.log('---');
            });
        }, 500);
    </script>
</body>
</html>
