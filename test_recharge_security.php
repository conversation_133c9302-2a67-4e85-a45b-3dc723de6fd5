<?php
/**
 * 充值安全加强测试脚本
 * 测试充值系统的安全防护机制
 */

include('confing/common.php');
require_once('security/recharge_security_middleware.php');

echo "=== 充值安全加强测试 ===\n\n";

// 初始化安全中间件
$security = new RechargeSecurityMiddleware($DB, '127.0.0.1');

echo "1. 安全表结构检查:\n";
try {
    // 检查安全日志表
    $result = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_log'");
    if ($result && $result->num_rows > 0) {
        echo "✓ 安全日志表已创建\n";
    } else {
        echo "❌ 安全日志表不存在\n";
    }
    
    // 检查安全令牌表
    $result = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_tokens'");
    if ($result && $result->num_rows > 0) {
        echo "✓ 安全令牌表已创建\n";
    } else {
        echo "❌ 安全令牌表不存在\n";
    }
} catch (Exception $e) {
    echo "❌ 表检查失败: " . $e->getMessage() . "\n";
}

echo "\n2. 充值安全检查测试:\n";

// 模拟用户数据
$test_uid = 1;
$test_amount = 100;
$test_order = 'TEST_' . time();

// 测试正常充值检查
$check_result = $security->beforeRecharge($test_uid, $test_amount, $test_order);
if ($check_result['allowed']) {
    echo "✓ 正常充值请求通过安全检查\n";
} else {
    echo "❌ 正常充值请求被阻止: " . $check_result['reason'] . "\n";
}

// 测试重复订单检查
$duplicate_check = $security->beforeRecharge($test_uid, $test_amount, $test_order);
if (!$duplicate_check['allowed'] && strpos($duplicate_check['reason'], '重复') !== false) {
    echo "✓ 重复订单检查正常工作\n";
} else {
    echo "⚠ 重复订单检查可能需要调整\n";
}

echo "\n3. 安全令牌测试:\n";

// 生成安全令牌
$token = $security->generateSecurityToken($test_uid, $test_amount);
if ($token) {
    echo "✓ 安全令牌生成成功: " . substr($token, 0, 16) . "...\n";
    
    // 验证令牌
    $valid = $security->validateSecurityToken($token, $test_uid);
    if ($valid) {
        echo "✓ 安全令牌验证成功\n";
    } else {
        echo "❌ 安全令牌验证失败\n";
    }
} else {
    echo "❌ 安全令牌生成失败\n";
}

echo "\n4. 并发检查测试:\n";

// 记录充值尝试
$security->afterRecharge($test_uid, $test_amount, $test_order, true);

// 检查并发
$concurrent = $security->checkConcurrentRecharge($test_uid);
echo $concurrent ? "⚠ 检测到并发充值\n" : "✓ 无并发充值检测\n";

echo "\n5. 安全加强措施总结:\n";
echo "✅ 数据库事务保护: 所有充值操作使用事务确保原子性\n";
echo "✅ 行级锁定: 使用FOR UPDATE防止并发竞态条件\n";
echo "✅ 订单状态检查: 原子性检查和更新订单状态\n";
echo "✅ 重复请求防护: 检查订单是否已处理\n";
echo "✅ 请求频率限制: 限制IP请求频率\n";
echo "✅ 可疑行为监控: 检测异常充值模式\n";
echo "✅ 安全日志记录: 完整的安全事件日志\n";
echo "✅ 安全令牌机制: 防止请求伪造\n";

echo "\n6. 防护机制详解:\n";
echo "🔒 并发保护:\n";
echo "   - 使用数据库事务包装所有充值操作\n";
echo "   - 使用FOR UPDATE行锁防止竞态条件\n";
echo "   - 原子性检查订单状态并更新\n";

echo "\n🔒 重复请求保护:\n";
echo "   - 检查订单是否已处理完成\n";
echo "   - 记录订单处理状态防止重复\n";
echo "   - 区分正常重复访问和异常重复请求\n";

echo "\n🔒 频率限制保护:\n";
echo "   - 每分钟最大10次充值请求\n";
echo "   - 每小时最大50次充值请求\n";
echo "   - 超限自动临时封禁\n";

echo "\n🔒 异常行为检测:\n";
echo "   - 短时间内大额充值监控\n";
echo "   - 频繁充值行为检测\n";
echo "   - 用户状态异常检查\n";

echo "\n7. 建议的安全测试:\n";
echo "1. 模拟并发充值请求测试\n";
echo "2. 重复提交同一订单测试\n";
echo "3. 高频率充值请求测试\n";
echo "4. 异常金额充值测试\n";
echo "5. 网络中断时的数据一致性测试\n";

echo "\n8. 监控和维护:\n";
echo "- 定期运行安全监控脚本: php security/recharge_security_monitor.php\n";
echo "- 查看安全日志: SELECT * FROM qingka_wangke_security_log ORDER BY id DESC LIMIT 100\n";
echo "- 清理过期数据: 系统自动清理30天前的日志\n";

echo "\n=== 测试完成 ===\n";
echo "充值系统安全加强已完成，具备多层防护机制\n";
echo "建议在生产环境中监控安全日志，及时发现异常行为\n";
?>
