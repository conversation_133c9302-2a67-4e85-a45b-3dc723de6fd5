# 8090教育升级故障排除指南

## 🚨 常见问题及解决方案

### 问题1：字段名冲突错误
```
错误信息：#1052 - Column 'name' in field list is ambiguous
```

#### 原因分析
- SQL查询中使用了JOIN连接多个表
- 多个表都有相同的字段名（如name字段）
- 没有使用表别名明确指定字段来源

#### 解决方案
已修复原始升级脚本中的字段冲突问题。如果仍遇到此错误，请使用修复脚本：

```bash
# 使用修复脚本
mysql -u username -p database_name < sql/fix_field_conflicts.sql
```

### 问题2：字段已存在错误
```
错误信息：Duplicate column name 'check_course'
```

#### 解决方案
脚本已包含字段存在性检查，会自动跳过已存在的字段。如果仍有问题：

```sql
-- 手动检查字段是否存在
SHOW COLUMNS FROM qingka_wangke_class LIKE 'check_course';

-- 如果字段已存在但数据未更新，执行数据更新
UPDATE qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
SET c.check_course = CASE 
    WHEN c.content LIKE '%项目查课: 支持%' THEN '支持'
    WHEN c.content LIKE '%项目查课: 不支持%' THEN '不支持'
    ELSE '未知'
END
WHERE h.pt = '8090edu';
```

### 问题3：8090教育货源未找到
```
错误信息：未找到8090教育货源配置
```

#### 解决方案
```sql
-- 检查8090教育货源是否存在
SELECT * FROM qingka_wangke_huoyuan WHERE pt = '8090edu' OR name LIKE '%8090%';

-- 如果不存在，需要先配置8090教育货源
INSERT INTO qingka_wangke_huoyuan (name, pt, url, user, pass, status) 
VALUES ('8090教育', '8090edu', 'http://1.14.58.242:8090', '用户名', '密码', 1);
```

### 问题4：索引创建失败
```
错误信息：Duplicate key name 'idx_check_course'
```

#### 解决方案
```sql
-- 检查索引是否已存在
SHOW INDEX FROM qingka_wangke_class WHERE Key_name = 'idx_check_course';

-- 如果需要重建索引
DROP INDEX idx_check_course ON qingka_wangke_class;
ALTER TABLE qingka_wangke_class ADD INDEX idx_check_course (check_course);
```

## 🔧 升级步骤详解

### 步骤1：备份数据
```bash
# 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份关键文件
cp Checkorder/ckjk.php Checkorder/ckjk.php.backup
cp api/8090edu.php api/8090edu.php.backup
```

### 步骤2：执行升级脚本
```bash
# 方案A：使用原始升级脚本（推荐）
mysql -u username -p database_name < sql/8090edu_check_course_upgrade.sql

# 方案B：如果遇到字段冲突，使用修复脚本
mysql -u username -p database_name < sql/fix_field_conflicts.sql
```

### 步骤3：验证升级结果
```bash
# 使用验证脚本检查升级结果
mysql -u username -p database_name < sql/verify_8090edu_upgrade.sql
```

### 步骤4：重新同步项目
```bash
# 重新同步8090教育项目，更新查课支持状态
php api/8090edu.php
```

### 步骤5：功能测试
```bash
# 运行测试脚本
php test_8090edu_optimization.php
```

## 📊 验证检查清单

### ✅ 数据库结构检查
- [ ] check_course字段已添加
- [ ] exam_support字段已添加  
- [ ] site_status字段已添加
- [ ] idx_check_course索引已创建
- [ ] idx_docking_check_course索引已创建

### ✅ 数据完整性检查
- [ ] 8090教育项目总数正常
- [ ] 查课支持状态已正确分类
- [ ] 考试支持状态已正确分类
- [ ] 网站状态已正确分类
- [ ] 无异常数据（空值、无效值）

### ✅ 功能验证检查
- [ ] 支持查课的项目：查课成功返回课程列表
- [ ] 支持查课的项目：查课失败返回错误信息
- [ ] 不支持查课的项目：直接返回账号密码格式
- [ ] 未知状态项目：尝试查课，失败时返回错误

## 🛠️ 手动修复命令

### 修复字段冲突
```sql
-- 如果升级脚本因字段冲突失败，手动执行以下命令

-- 1. 添加字段（如果不存在）
ALTER TABLE qingka_wangke_class 
ADD COLUMN IF NOT EXISTS check_course VARCHAR(20) DEFAULT '未知' COMMENT '查课支持状态：支持/不支持/未知';

ALTER TABLE qingka_wangke_class 
ADD COLUMN IF NOT EXISTS exam_support VARCHAR(20) DEFAULT '未知' COMMENT '考试支持状态：支持/不支持/未知';

ALTER TABLE qingka_wangke_class 
ADD COLUMN IF NOT EXISTS site_status VARCHAR(20) DEFAULT '正常' COMMENT '网站状态：正常/维护中/异常';

-- 2. 更新数据（使用表别名避免冲突）
UPDATE qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
SET 
    c.check_course = CASE 
        WHEN c.content LIKE '%项目查课: 支持%' THEN '支持'
        WHEN c.content LIKE '%项目查课: 不支持%' THEN '不支持'
        ELSE '未知'
    END,
    c.exam_support = CASE 
        WHEN c.content LIKE '%项目考试: 支持%' THEN '支持'
        WHEN c.content LIKE '%项目考试: 不支持%' THEN '不支持'
        ELSE '未知'
    END,
    c.site_status = CASE 
        WHEN c.content LIKE '%项目状态: 正常%' THEN '正常'
        WHEN c.content LIKE '%项目状态: 维护中%' THEN '维护中'
        WHEN c.content LIKE '%项目状态: 异常%' THEN '异常'
        ELSE '正常'
    END
WHERE h.pt = '8090edu';

-- 3. 添加索引
ALTER TABLE qingka_wangke_class ADD INDEX IF NOT EXISTS idx_check_course (check_course);
ALTER TABLE qingka_wangke_class ADD INDEX IF NOT EXISTS idx_docking_check_course (docking, check_course);
```

### 验证修复结果
```sql
-- 检查字段和数据
SELECT 
    c.check_course,
    COUNT(*) as count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu' AND c.status = 1
GROUP BY c.check_course;
```

## 📞 技术支持

### 日志查看
```bash
# 查看MySQL错误日志
tail -f /var/log/mysql/error.log

# 查看PHP错误日志
tail -f /var/log/php/error.log
```

### 回滚方案
如果升级出现严重问题，可以回滚：

```bash
# 恢复数据库备份
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql

# 恢复代码文件
cp Checkorder/ckjk.php.backup Checkorder/ckjk.php
cp api/8090edu.php.backup api/8090edu.php
```

### 联系支持
如果遇到无法解决的问题：
1. 收集错误日志和错误信息
2. 记录执行的具体步骤
3. 提供数据库结构和数据样例
4. 描述期望的结果和实际结果

## 🎯 升级成功标志

升级成功后，您应该看到：

1. **数据库结构**：新增了3个字段和2个索引
2. **项目分类**：8090教育项目按查课支持状态正确分类
3. **功能验证**：查课逻辑根据项目类型智能处理
4. **性能提升**：无查项目响应速度显著提升
5. **错误处理**：查课失败时返回明确的错误信息

完成升级后，8090教育的查课功能将更加智能、高效和可靠！
