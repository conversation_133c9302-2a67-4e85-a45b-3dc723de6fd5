<?php
$title = '对接管理';
require_once('head.php');
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
?>
  <style>
.form-row {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-bottom: 15px;
}
.form-group {
    flex: 1;
    margin-right: 10px;
    min-width: 200px;
}
.form-group:last-child {
    margin-right: 0;
}
@media (max-width: 768px) {
    .form-group {
        flex: 100%;
        margin-right: 0;
        margin-bottom: 10px;
        min-width: auto;
    }
}
.category-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.category-title {
    flex-grow: 1;
    margin-left: 10px;
}
.category-search {
    flex-shrink: 0;
    width: 200px;
    margin-left: 10px;
}
.category-actions {
    flex-shrink: 0;
    margin-left: 10px;
}
.selected-count-badge {
    background-color: #409EFF;
    border-radius: 10px;
    color: white;
    padding: 2px 6px;
    font-size: 12px;
    margin-left: 10px;
}
.el-table th, .el-table td {
    white-space: nowrap;
}
.el-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}
.el-collapse-item__header {
    font-weight: bold;
}
</style> 
  <div class="app-content-body"> 
   <div class="wrapper-md control" id="orderlist"> 
    <div class="panel panel-default"> 
     <div class="panel-heading font-bold layui-bg-black">
      &nbsp;综合对接——本插件由挖友之家制作，请支持正版 官方频道 t.me/wjwk666 
     </div> 
     <div class="panel-body"> 
      <!-- 货源选择和查询区域 --> 
      <el-card shadow="hover"> 
       <div class="form-row"> 
        <div class="form-group col-md-6"> 
         <label for="keyword">选择货源</label> 
         <el-select id="select" v-model="cx.hid" filterable="" placeholder="选择货源" style="width:100%"> 
                            <el-option label="输入关键词搜索并选择货源" value=""></el-option>
                            <?php
                            $a = $DB->query("select * from qingka_wangke_huoyuan");
                            while ($row = $DB->fetch($a)) {
                                echo '<el-option label="' .'hid '.$row['hid'] .' '. $row['name'] . '" value="' . $row['hid'] . '"></el-option>';
                            }
                            ?>
                        </el-select>
        </div> 
        <div class="form-group col-md-3"> 
         <label>&nbsp;</label> 
         <button class="btn btn-info btn-block" @click="checkBalance">查询货源余额</button> 
        </div> 
        <div class="form-group col-md-3"> 
         <label>&nbsp;</label> 
         <button class="btn btn-info btn-block" @click="checkDeployedCount">查询货源已上架数</button> 
        </div> 
       </div> 
      </el-card> 
      <!-- 上架配置和操作区域 --> 
      <el-card shadow="hover" class="mb-4"> 
       <!-- Row 1: Fetch Products --> 
       <div class="form-row"> 
        <div class="form-group col-md-8">
         <label>&nbsp;</label>
         <el-button type="warning" @click="fetchAllProducts" :disabled="!cx.hid" style="width:100%">
           获取商品
         </el-button>
        </div>
        <div class="form-group col-md-4">
         <label>&nbsp;</label>
         <el-button type="info" @click="test29Categories" :disabled="!cx.hid" style="width:100%">
           测试29分类API
         </el-button>
        </div>
       </div> 
       <!-- Row 2: Create New Category & Local Category Select --> 
       <div class="form-row"> 
        <div class="form-group col-md-6"> 
         <label for="createNewCategory">是否新建分类</label> 
         <el-select id="createNewCategory" v-model="createNewCategory" @change="toggleCategoryOptions" style="width:100%"> 
          <el-option label="否" value="0"></el-option> 
          <el-option label="是" value="1"></el-option> 
         </el-select> 
        </div> 
        <div class="form-group col-md-6" v-if="createNewCategory === '0'"> 
         <label for="localCategorySelect">上架分类</label> 
         <el-select id="localCategorySelect" v-model="localCategoryId" filterable="" placeholder="请选择分类" style="width:100%" :disabled="createNewCategory === '1'"> 
            <el-option label="选择上架分类" value=""></el-option>
            <?php 
            $categories = $DB->query("select * from qingka_wangke_fenlei");
            while ($row = $DB->fetch($categories)) {
                echo '<el-option label="' .'ID '.$row['id'] .' '. $row['name'] . '" value="' . $row['id'] . '"></el-option>';
            }
            ?>
        </el-select>
        </div> 
        <div class="form-group col-md-6" v-if="createNewCategory === '1'"> 
         <label for="newCategoryName">新建分类名称</label> 
         <el-input v-model="newCategoryName" placeholder="请输入分类名称" :disabled="createNewCategory === '0'"></el-input> 
        </div> 
       </div> 
       <!-- Row 3: Pricing, Calculation Method, Existing Products, and Selected Products --> 
       <div class="form-row"> 
        <div class="form-group col-md-3"> 
         <label for="markupMultiplier">设置上架价格</label> 
         <el-input v-model="markupMultiplier" placeholder="加价"></el-input> 
        </div> 
        <div class="form-group col-md-3"> 
         <label>加价计算方式</label> 
         <el-select v-model="multiplyByFive" style="width:100%"> 
          <el-option value="2" label="乘法计算且乘5(29)"></el-option> 
          <el-option value="1" label="乘法计算且不乘5(暗网)"></el-option> 
          <el-option value="0" label="加法计算直接加价"></el-option> 
         </el-select> 
        </div> 
        <div class="form-group col-md-3"> 
         <label>处理已有商品</label> 
         <el-select v-model="skipExisting" style="width:100%"> 
          <el-option value="1" label="跳过已有商品"></el-option> 
          <el-option value="0" label="不跳过"></el-option> 
         </el-select> 
        </div> 
        <div class="form-group col-md-3"> 
         <label>&nbsp;</label> 
         <el-button type="success" @click="startIntegrationSelected" :disabled="totalSelectedCount === 0" style="width:100%">
           上架选中商品 ({{ totalSelectedCount }}) 
         </el-button> 
        </div> 
       </div> 
       <!-- Row 4: Upstream Category and Integration by Category --> 
       <div class="form-row"> 
        <div class="form-group col-md-6"> 
         <label>上游分类ID</label> 
         <el-select v-model="upstreamCategoryId" placeholder="选择上游分类" style="width:100%" filterable=""> 
          <el-option label="全部分类" value="all"></el-option> 
          <el-option v-for="option in upstreamCategoryOptions" :key="option.value" :label="option.label" :value="option.value"></el-option> 
         </el-select> 
        </div> 
        <div class="form-group col-md-6"> 
         <label>&nbsp;</label> 
         <el-button type="success" @click="startIntegrationByCategory" :disabled="!cx.hid || !upstreamCategoryId" style="width:100%">
           直接按上游分类上架(速度更快) 
         </el-button> 
        </div> 
       </div> 
      </el-card> 
      <!-- 商品搜索和列表区域 --> 
      <el-card shadow="hover" v-if="groupedProducts.length &gt; 0"> 
       <div class="form-row"> 
        <!-- 全局搜索商品名称 --> 
        <!-- 全局搜索商品名称 --> 
        <div class="form-group col-md-6"> 
         <label>全局搜索商品名称</label> 
         <div class="input-group"> 
          <input type="text" class="form-control" v-model="tempGlobalSearchName" placeholder="输入关键词后点击搜索" /> 
          <span class="input-group-btn"> <button class="btn btn-default" @click="applyGlobalSearch" :disabled="isSearching"> <span v-if="isSearching"><i class="fa fa-spinner fa-spin"></i> 搜索中...</span> <span v-else="">搜索</span> </button> </span> 
         </div> 
        </div> 
        <!-- 全局过滤分类ID --> 
        <div class="form-group col-md-6"> 
         <label>全局过滤分类ID</label> 
         <div class="input-group"> 
          <input type="text" class="form-control" v-model="tempGlobalSearchFenlei" placeholder="输入分类ID后点击搜索" /> 
          <span class="input-group-btn"> <button class="btn btn-default" @click="applyGlobalSearch" :disabled="isSearching"> <span v-if="isSearching"><i class="fa fa-spinner fa-spin"></i> 搜索中...</span> <span v-else="">搜索</span> </button> </span> 
         </div> 
        </div> 
       </div> 
       <div style="margin-bottom: 10px; display: flex; align-items: center;"> 
        <span>已选中商品: {{ totalSelectedCount }}</span> 
        <button class="btn btn-primary btn-sm" style="margin-left: 10px;" @click="clearAllSelections">清空选择</button> 
       </div> 
       <el-collapse v-model="activeCategoryPanels" accordion=""> 
        <el-collapse-item v-for="group in filteredGroupedProducts" :key="group.fenlei" :name="group.fenlei"> 
         <template slot="title"> 
          <div class="category-panel-header"> 
           <el-checkbox :indeterminate="isCategoryPartiallySelected(group.fenlei)" v-model="selectedCategories[group.fenlei]" @change="(val) =&gt; toggleCategorySelection(group.fenlei, val)"></el-checkbox> 
           <span class="category-title">{{ group.fenlei_display || ('分类ID: ' + group.fenlei) }} (共 {{ group.products.length }} 个商品)</span>
           <span v-if="getCategorySelectedCount(group.fenlei) &gt; 0" class="selected-count-badge"> 已选 {{ getCategorySelectedCount(group.fenlei) }} </span> 
           <div class="category-search"> 
            <input type="text" class="form-control input-sm" :placeholder="`搜索分类 ${group.fenlei} 商品`" v-model="categorySearchTerms[group.fenlei]" @input="(e) =&gt; updateCategorySearch(group.fenlei, e.target.value)" /> 
           </div> 
           <div class="category-actions"> 
            <button class="btn btn-default btn-xs" @click.stop="expandAllPanels" v-if="group.fenlei === filteredGroupedProducts[0]?.fenlei"> 展开全部 </button> 
            <button class="btn btn-default btn-xs" @click.stop="collapseAllPanels" v-if="group.fenlei === filteredGroupedProducts[0]?.fenlei" style="margin-left: 5px;"> 收起全部 </button> 
           </div> 
          </div> 
         </template> 
         <el-table :data="getFilteredProductsForCategory(group)" border="" stripe="" size="small" style="width: 100%" max-height="400" @selection-change="(selection) =&gt; handleTableSelectionChange(group.fenlei, selection)" :row-key="(row) =&gt; row.cid" ref="categoryTables"> 
          <el-table-column type="selection" width="50" :reserve-selection="true"></el-table-column>
          <el-table-column label="商品名" prop="name"></el-table-column>
          <el-table-column label="CID" prop="cid" width="90"></el-table-column>
          <el-table-column label="分类信息" width="180" v-if="is29System">
            <template slot-scope="scope">
              <span style="font-size: 12px; color: #666;">
                ID:{{ scope.row.fenlei_id }} - {{ scope.row.fenlei_name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="价格" prop="price" width="80"></el-table-column>
         </el-table> 
        </el-collapse-item> 
       </el-collapse> 
      </el-card> 
      <div v-else-if="products.length &gt; 0"> 
       <p>正在按分类组织商品，请稍候...</p> 
      </div> 
      <div v-else=""> 
      </div> 
      <!-- 高级操作区域，使用折叠面板 --> 
      <el-card shadow="hover"> 
       <el-collapse v-model="advancedActiveNames"> 
        <el-collapse-item title="高阶批量操作模块" name="1"> 
         <div class="form-row"> 
          <div class="form-group col-md-3"> 
           <label>更新商品信息 (上游分类ID)</label> 
           <input type="text" class="form-control" v-model="upstreamCategoryIdForPrice" placeholder="默认全部更新,可填写上游分类ID指定更新" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>加价</label> 
           <input type="text" class="form-control" v-model="priceRatio" placeholder="加价" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>计算方式</label> 
           <select class="form-control" v-model="multiplyByFiveForPrice"> <option value="2">乘法计算且乘5(29)</option> <option value="1">乘法计算且不乘5(暗网)</option> <option value="0">加法计算直接加价</option> <option value="3">不更新价格只更新介绍</option> <option value="4">不更新价格只更新介绍和商品名</option> <option value="5">同步上游下架商品（不会删除）</option> </select> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>&nbsp;</label> 
           <button class="btn btn-danger btn-block" @click="updatePrice">更新价格</button> 
          </div> 
         </div> 
         <div class="form-row"> 
          <div class="form-group col-md-3"> 
           <label>批量替换关键词</label> 
           <input type="text" class="form-control" v-model="oldKeyword" placeholder="请输入要替换的关键词" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>替换为</label> 
           <input type="text" class="form-control" v-model="newKeyword" placeholder="请输入替换后的关键词,留空删除关键词" /> 
          </div> 
          <div class="form-group col-md-2"> 
           <label>生效范围</label> 
           <select class="form-control" v-model="effectScope"> <option value="category">分类ID=</option> <option value="docking">对接平台ID=</option> <option value="all">对所有范围执行</option> </select> 
          </div> 
          <div class="form-group col-md-2"> 
           <label>范围ID</label> 
           <input type="text" class="form-control" v-model="scopeId" placeholder="请输入分类ID或对接平台ID" :disabled="effectScope === 'all'" /> 
          </div> 
          <div class="form-group col-md-2"> 
           <label>&nbsp;</label> 
           <button class="btn btn-dark btn-block" @click="updateKeywords">更新关键词</button> 
          </div> 
         </div> 
         <div class="form-row"> 
          <div class="form-group col-md-3"> 
           <label>批量添加商品前缀</label> 
           <input type="text" class="form-control" v-model="prefix" placeholder="请输入要新增的前缀" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>生效范围</label> 
           <select class="form-control" v-model="prefixEffectScope"> <option value="category">分类ID=</option> <option value="docking">对接平台ID=</option> </select> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>范围ID</label> 
           <input type="text" class="form-control" v-model="prefixScopeId" placeholder="请输入分类ID或对接平台ID" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>&nbsp;</label> 
           <button class="btn btn-dark btn-block" @click="addPrefix">添加前缀</button> 
          </div> 
         </div> 
         <div class="form-row"> 
          <div class="form-group col-md-3"> 
           <label>删除商品</label> 
           <select class="form-control" v-model="deleteDuplicateScope"> <option value="all">所有范围</option> <option value="category">分类ID=</option> </select> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>范围ID</label> 
           <input type="text" class="form-control" v-model="deleteDuplicateScopeId" placeholder="请输入分类ID" :disabled="deleteDuplicateScope === 'all'" /> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>保留策略</label> 
           <select class="form-control" v-model="deleteDuplicateStrategy"> <option value="keep_larger">保留CID更大的商品</option> <option value="keep_smaller">保留CID更小的商品</option> <option value="delall">直接删除</option> </select> 
          </div> 
          <div class="form-group col-md-3"> 
           <label>&nbsp;</label> 
           <button class="btn btn-dark btn-block" @click="deleteDuplicates">删除商品</button> 
          </div> 
         </div> 
        </el-collapse-item> 
       </el-collapse> 
      </el-card> 
     </div> 
    </div> 
   </div> 
  </div> 
<?php require_once("footer.php");?> 
  <script>
new Vue({
  el: "#orderlist",
  data: {
    cx: { hid: "" },
    createNewCategory: '0',
    newCategoryName: '',
    products: [],
    groupedProducts: [],
    upstreamCategories: {},  // 存储上游分类信息
    is29System: false,      // 是否为29系统
    selectedCidsPerCategory: {},
    selectedCategories: {},
    localCategoryId: '',
    markupMultiplier: '',
    multiplyByFive: '2',
    skipExisting: '1',
    upstreamCategoryIdForPrice: '',
    upstreamCategoryId: 'all',  // 默认'all'
    upstreamCategoryOptions: [],
    priceRatio: '',
    multiplyByFiveForPrice: '2',
    oldKeyword: '',
    newKeyword: '',
    effectScope: 'all',
    scopeId: '',
    prefix: '',
    prefixEffectScope: 'category',
    prefixScopeId: '',
    deleteDuplicateScope: 'all',
    deleteDuplicateScopeId: '',
    deleteDuplicateStrategy: 'keep_larger',
    globalSearchName: '',
    globalSearchFenlei: '',
    categorySearchTerms: {},
    activeCategoryPanels: '',
    filteredProductsCache: {},
    tempGlobalSearchName: '',     // 临时搜索关键词（商品名）
    tempGlobalSearchFenlei: '',   // 临时搜索关键词（分类ID）
    appliedGlobalSearchName: '',  // 实际用于过滤的关键词（点击按钮后才更新）
    appliedGlobalSearchFenlei: '',// 实际用于过滤的分类ID（点击按钮后才更新）
     isSearching: false,
    advancedActiveNames: ['0']  // 默认展开高级操作
  },
  computed: {
filteredGroupedProducts() {
        if (this.groupedProducts.length === 0) return [];
        return this.groupedProducts.filter(group => {
            const globalFenleiMatch = this.appliedGlobalSearchFenlei === '' || group.fenlei.toString() === this.appliedGlobalSearchFenlei.trim();
            if (!globalFenleiMatch) return false;
            if (this.appliedGlobalSearchName.trim() !== '') {
                const hasMatchingProduct = group.products.some(p =>
                    p.name.toLowerCase().includes(this.appliedGlobalSearchName.toLowerCase().trim())
                );
                if (!hasMatchingProduct) return false;
            }
            return true;
        }).map(group => {
            const searchTerm = this.categorySearchTerms[group.fenlei] || '';
            let filteredProducts = group.products;
            if (searchTerm.trim() !== '') {
                filteredProducts = filteredProducts.filter(p =>
                    p.name.toLowerCase().includes(searchTerm.toLowerCase().trim())
                );
            }
            if (this.appliedGlobalSearchName.trim() !== '' && (searchTerm.trim() === '' || this.appliedGlobalSearchName.toLowerCase().trim() !== searchTerm.toLowerCase().trim())) {
                filteredProducts = filteredProducts.filter(p =>
                    p.name.toLowerCase().includes(this.appliedGlobalSearchName.toLowerCase().trim())
                );
            }
            return {
                ...group,
                products: filteredProducts
            };
        });
    },
    totalSelectedCount() {
        let count = 0;
        for (const fenlei in this.selectedCidsPerCategory) {
            count += this.selectedCidsPerCategory[fenlei].length;
        }
        return count;
    }
  },
  methods: {
          applyGlobalSearch() {
        this.isSearching = true;  // 开始搜索
        // 使用 setTimeout 来模拟异步操作，让用户看到加载效果
        setTimeout(() => {
            this.appliedGlobalSearchName = this.tempGlobalSearchName;
            this.appliedGlobalSearchFenlei = this.tempGlobalSearchFenlei;
            this.isSearching = false;  // 搜索完成
        }, 100);  // 100ms 延迟，让用户能明显看到加载状态
    },
    fetchAllProducts: function() {
        if (!this.cx.hid) {
            layer.msg('请选择货源接口', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=fetchAllProducts", {
            hid: this.cx.hid
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                this.products = data.data.products;
                this.upstreamCategories = data.data.categories || {}; // 保存上游分类信息
                this.is29System = data.data.is_29_system || false; // 保存是否为29系统
                this.groupProductsByCategory();
                this.selectedCidsPerCategory = {};
                this.selectedCategories = {};
                this.categorySearchTerms = {};
                this.activeCategoryPanels = '';
                this.filteredProductsCache = {};

                // 显示获取到的分类信息和调试信息
                if (data.data.debug) {
                    console.log('详细调试信息:', data.data.debug);
                    if (data.data.debug.category_api_attempts) {
                        console.log('分类API尝试结果:', data.data.debug.category_api_attempts);
                    }
                    if (data.data.debug.sample_product) {
                        console.log('示例商品数据:', data.data.debug.sample_product);
                    }
                }

                if (this.is29System) {
                    if (Object.keys(this.upstreamCategories).length > 0) {
                        console.log('获取到29系统分类信息:', this.upstreamCategories);
                        layer.msg(`成功获取29系统商品(${data.data.debug.products_count})和分类信息(${data.data.debug.categories_count})`, {icon: 1});
                    } else {
                        console.log('29系统分类获取失败，详细调试信息:', data.data.debug);
                        // 显示更详细的错误信息
                        var errorMsg = `获取29系统商品(${data.data.debug.products_count})成功，但未获取到分类信息。`;
                        if (data.data.debug.sample_product && data.data.debug.sample_product.fenleiname) {
                            errorMsg += `商品中包含fenleiname字段，请检查控制台调试信息。`;
                        } else {
                            errorMsg += `商品中无fenleiname字段，请使用"测试29分类API"功能诊断。`;
                        }
                        layer.msg(errorMsg, {icon: 2, time: 5000});
                    }
                } else {
                    layer.msg(`成功获取商品信息(${data.data.debug.products_count})`, {icon: 1});
                }
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        }.bind(this)).catch(function(error) {
            layer.close(load);
            layer.msg('获取商品失败', {icon: 2});
        });
    },
    test29Categories: function() {
        if (!this.cx.hid) {
            layer.msg('请先选择货源', {icon: 2});
            return;
        }

        var load = layer.load(2, {shade: [0.1, '#fff']});
        this.$http.post("/apiadmin.php?act=test29Categories", {
            hid: this.cx.hid
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                console.log('29系统分类API测试结果:', data.data);

                // 构建测试结果显示内容
                var resultHtml = '<div style="max-height: 400px; overflow-y: auto;">';
                resultHtml += '<h4>平台信息:</h4>';
                resultHtml += '<p>名称: ' + data.data.platform_info.name + '</p>';
                resultHtml += '<p>类型: ' + data.data.platform_info.pt + '</p>';
                resultHtml += '<p>URL: ' + data.data.platform_info.url + '</p>';
                resultHtml += '<h4>API测试结果:</h4>';

                for (var apiName in data.data.test_results) {
                    var result = data.data.test_results[apiName];
                    var borderColor = result.categories_found > 0 ? '#52c41a' : '#ff4d4f';
                    resultHtml += '<div style="border: 2px solid ' + borderColor + '; margin: 10px 0; padding: 10px;">';
                    resultHtml += '<h5>' + apiName + ' API ' + (result.categories_found > 0 ? '✅' : '❌') + '</h5>';
                    resultHtml += '<p><strong>URL:</strong> ' + result.url + '</p>';
                    resultHtml += '<p><strong>响应时间:</strong> ' + result.response_time + '</p>';
                    resultHtml += '<p><strong>响应长度:</strong> ' + result.response_length + ' 字符</p>';
                    resultHtml += '<p><strong>是否JSON:</strong> ' + (result.is_json ? '是' : '否') + '</p>';
                    resultHtml += '<p><strong>找到分类数:</strong> ' + result.categories_found + '</p>';

                    if (result.sample_categories && result.sample_categories.length > 0) {
                        resultHtml += '<p><strong>示例分类:</strong><br>';
                        result.sample_categories.forEach(function(cat, index) {
                            resultHtml += '&nbsp;&nbsp;• ID:' + cat.id + ' - ' + cat.name + '<br>';
                        });
                        resultHtml += '</p>';
                    }

                    // 显示响应预览
                    resultHtml += '<details style="margin-top: 10px;">';
                    resultHtml += '<summary><strong>原始响应预览</strong> (点击展开)</summary>';
                    resultHtml += '<pre style="background: #f5f5f5; padding: 10px; margin: 5px 0; font-size: 12px; overflow-x: auto;">';
                    resultHtml += result.response_preview.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                    resultHtml += '</pre>';
                    resultHtml += '</details>';

                    resultHtml += '</div>';
                }
                resultHtml += '</div>';

                layer.open({
                    type: 1,
                    title: '29系统分类API测试结果',
                    content: resultHtml,
                    area: ['80%', '70%'],
                    btn: ['关闭'],
                    yes: function(index) {
                        layer.close(index);
                    }
                });
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        }.bind(this)).catch(function(error) {
            layer.close(load);
            layer.msg('测试失败', {icon: 2});
            console.error('测试错误:', error);
        });
    },
    groupProductsByCategory: function() {
        const groups = {};
        this.products.forEach(product => {
            const fenlei = product.fenlei;
            if (!groups[fenlei]) {
                groups[fenlei] = {
                    fenlei: fenlei,
                    fenlei_id: product.fenlei_id,
                    fenlei_name: product.fenlei_name,
                    fenlei_display: product.fenlei_display,
                    products: []
                };
            }
            groups[fenlei].products.push(product);
        });
        this.groupedProducts = Object.values(groups);
        this.upstreamCategoryOptions = this.groupedProducts.map(g => ({
    value: g.fenlei,
    label: g.fenlei_display || `分类ID: ${g.fenlei} (共 ${g.products.length} 个商品)`
}));
    },
    getFilteredProductsForCategory(group) {
        return group.products;
    },
    handleTableSelectionChange(fenlei, selection) {
        this.$set(this.selectedCidsPerCategory, fenlei, selection.map(s => s.cid));
        const group = this.groupedProducts.find(g => g.fenlei === fenlei);
        if (group) {
            const totalInCategory = group.products.length;
            const selectedInCategory = selection.length;
            this.$set(this.selectedCategories, fenlei, selectedInCategory === totalInCategory);
        }
    },
    toggleCategorySelection(fenlei, isChecked) {
        const group = this.groupedProducts.find(g => g.fenlei === fenlei);
        if (!group) return;
        if (isChecked) {
            this.$set(this.selectedCidsPerCategory, fenlei, [...group.products.map(p => p.cid)]);
        } else {
            this.$set(this.selectedCidsPerCategory, fenlei, []);
        }
        this.$nextTick(() => {
            const tableRef = this.$refs.categoryTables ? this.$refs.categoryTables.find(table => table.data[0] && table.data[0].fenlei === fenlei) : null;
            if (tableRef) {
                if (isChecked) {
                    tableRef.clearSelection();
                    group.products.forEach(row => {
                        const rowInTableData = tableRef.data.find(d => d.cid === row.cid);
                        if (rowInTableData) {
                            tableRef.toggleRowSelection(rowInTableData, true);
                        }
                    });
                } else {
                    tableRef.clearSelection();
                }
            }
        });
    },
    isCategoryPartiallySelected(fenlei) {
        const selectedCids = this.selectedCidsPerCategory[fenlei] || [];
        const group = this.groupedProducts.find(g => g.fenlei === fenlei);
        if (!group) return false;
        const totalInCategory = group.products.length;
        const selectedInCategory = selectedCids.length;
        return selectedInCategory > 0 && selectedInCategory < totalInCategory;
    },
    getCategorySelectedCount(fenlei) {
        return (this.selectedCidsPerCategory[fenlei] || []).length;
    },
    updateCategorySearch(fenlei, value) {
        this.$set(this.categorySearchTerms, fenlei, value);
    },
    expandAllPanels() {
        this.activeCategoryPanels = this.filteredGroupedProducts.map(g => g.fenlei);
    },
    collapseAllPanels() {
        this.activeCategoryPanels = '';
    },
    clearAllSelections() {
        this.selectedCidsPerCategory = {};
        for (const fenlei in this.selectedCategories) {
            this.$set(this.selectedCategories, fenlei, false);
        }
        this.$nextTick(() => {
            if (this.$refs.categoryTables) {
                this.$refs.categoryTables.forEach(table => {
                    if (table && typeof table.clearSelection === 'function') {
                        table.clearSelection();
                    }
                });
            }
        });
    },
    toggleCategoryOptions: function() {
        if (this.createNewCategory === '1') {
            this.localCategoryId = '';
        }
    },
    startIntegrationSelected: function() {
        if (this.createNewCategory === '1' && !this.newCategoryName) {
            layer.msg('请输入新建分类的名称', {icon: 2});
            return;
        }
        if (!this.cx.hid) {
            layer.msg('请选择货源接口', {icon: 2});
            return;
        }
        if (this.createNewCategory === '0' && !this.localCategoryId) {
            layer.msg('请选择上架分类', {icon: 2});
            return;
        }
        let allSelectedCids = [];
        for (const fenlei in this.selectedCidsPerCategory) {
            allSelectedCids = [...allSelectedCids, ...this.selectedCidsPerCategory[fenlei]];
        }
        allSelectedCids = [...new Set(allSelectedCids)];
        if (allSelectedCids.length === 0) {
            layer.msg('请选择要上架的商品', {icon: 2});
            return;
        }
        if (!this.markupMultiplier) {
            layer.msg('请输入加价', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=startIntegrationSelected", {
            hid: this.cx.hid,
            localCategoryId: this.createNewCategory === '1' ? '' : this.localCategoryId,
            cids: JSON.stringify(allSelectedCids),
            markupMultiplier: this.markupMultiplier,
            multiplyByFive: this.multiplyByFive,
            skipExisting: this.skipExisting,
            createNewCategory: this.createNewCategory,
            newCategoryName: this.newCategoryName
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.msg(data.data.msg, {icon: 1});
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        }).catch(function(error) {
            layer.close(load);
            layer.msg('上架请求失败', {icon: 2});
        });
    },
    startIntegrationByCategory: function() {
    if (this.createNewCategory === '1' && !this.newCategoryName) {
        layer.msg('请输入新建分类的名称', {icon: 2});
        return;
    }
    if (!this.cx.hid) {
        layer.msg('请选择货源接口', {icon: 2});
        return;
    }
    if (this.createNewCategory === '0' && !this.localCategoryId) {
        layer.msg('请选择上架分类', {icon: 2});
        return;
    }
    if (!this.upstreamCategoryId) {
        layer.msg('请选择上游分类', {icon: 2});
        return;
    }
    if (!this.markupMultiplier) {
        layer.msg('请输入加价', {icon: 2});
        return;
    }
    var load = layer.load(2);
    this.$http.post("/apiadmin.php?act=startintegration", {
        hid: this.cx.hid,
        upstreamCategoryId: this.upstreamCategoryId,
        localCategoryId: this.createNewCategory === '1' ? '' : this.localCategoryId,
        markupMultiplier: this.markupMultiplier,
        multiplyByFive: this.multiplyByFive,
        skipExisting: this.skipExisting,
        createNewCategory: this.createNewCategory,
        newCategoryName: this.newCategoryName
    }, {emulateJSON: true}).then(function(data) {
        layer.close(load);
        if (data.data.code == 1) {
            layer.msg(data.data.msg, {icon: 1});
        } else {
            layer.msg(data.data.msg, {icon: 2});
        }
    }).catch(function(error) {
        layer.close(load);
        layer.msg('上架请求失败', {icon: 2});
    });
},
    checkBalance: function() {
        if (!this.cx.hid) {
            layer.msg('请选择货源', { icon: 2 });
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=checkbalance", {
            hid: this.cx.hid
        }, { emulateJSON: true }).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.alert(data.data.msg, { icon: 1, title: "信息" });
            } else {
                layer.msg(data.data.msg, { icon: 2 });
            }
        });
    },
    checkDeployedCount: function() {
        if (!this.cx.hid) {
            layer.msg('请选择货源', { icon: 2 });
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=checkdeployedcount", {
            hid: this.cx.hid
        }, { emulateJSON: true }).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.alert(data.data.msg, { icon: 1, title: "信息" });
            } else {
                layer.msg(data.data.msg, { icon: 2 });
            }
        });
    },
    updatePrice: function() {
        if (!this.cx.hid) {
            layer.msg('请选择货源接口', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=updateprice", {
            hid: this.cx.hid,
            upstreamCategoryId: this.upstreamCategoryIdForPrice,
            priceRatio: this.priceRatio,
            multiplyByFive: this.multiplyByFiveForPrice
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.msg(data.data.msg, {icon: 1});
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        });
    },
    updateKeywords: function() {
        if (!this.oldKeyword) {
            layer.msg('请输入要替换的关键词', {icon: 2});
            return;
        }
        if (this.effectScope !== 'all' && !this.scopeId) {
            layer.msg('请输入分类ID或对接平台ID', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=updatekeywords", {
            oldKeyword: this.oldKeyword,
            newKeyword: this.newKeyword,
            effectScope: this.effectScope,
            scopeId: this.scopeId
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.msg(data.data.msg, {icon: 1});
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        });
    },
    addPrefix: function() {
        if (!this.prefix) {
            layer.msg('请输入要新增的前缀', {icon: 2});
            return;
        }
        if (!this.prefixScopeId) {
            layer.msg('请输入分类ID或对接平台ID', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=addprefix", {
            prefix: this.prefix,
            prefixEffectScope: this.prefixEffectScope,
            prefixScopeId: this.prefixScopeId
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.msg(data.data.msg, {icon: 1});
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        });
    },
    deleteDuplicates: function() {
        if (this.deleteDuplicateScope !== 'all' && !this.deleteDuplicateScopeId) {
            layer.msg('请输入分类ID或对接接口ID', {icon: 2});
            return;
        }
        var load = layer.load(2);
        this.$http.post("/apiadmin.php?act=deleteDuplicates", {
            scope: this.deleteDuplicateScope,
            scopeId: this.deleteDuplicateScopeId,
            strategy: this.deleteDuplicateStrategy
        }, {emulateJSON: true}).then(function(data) {
            layer.close(load);
            if (data.data.code == 1) {
                layer.msg(data.data.msg, {icon: 1});
            } else {
                layer.msg(data.data.msg, {icon: 2});
            }
        });
    }
  }
});
</script>
 </body>
</html>