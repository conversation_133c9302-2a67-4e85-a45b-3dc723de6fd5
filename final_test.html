<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复测试</title>
    <link rel="stylesheet" href="assets/css/select-optimization.css">
    <link rel="stylesheet" href="assets/css/simple-scrollbar.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-option {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 10px;
            background: white;
        }
        .test-option:hover {
            background: #f8f9fa;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #155724;
        }
        .check-item {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .check-pass {
            background: #d4edda;
            color: #155724;
        }
        .check-fail {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 最终修复验证</h1>
        
        <div class="success">
            <strong>✅ 修复目标：</strong><br>
            1. 价格标签显示在前面<br>
            2. 项目名称显示在后面<br>
            3. 长项目名称有滚动条<br>
            4. 两个元素都完全可见
        </div>

        <div id="test-results"></div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">50积分</span>
                <span class="project-name">这是一个非常非常非常长的项目名称，用来测试价格和名称是否都能正确显示，并且有滚动条</span>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">100积分</span>
                <span class="project-name">短名称</span>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">200积分</span>
                <span class="project-name">中等长度的项目名称测试案例，应该也有滚动条</span>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">300积分</span>
                <span class="project-name">超级超级超级超级超级超级长的项目名称，包含很多很多很多文字内容，需要滚动才能看完整，测试滚动条功能</span>
            </div>
        </div>
    </div>

    <script>
        // 自动检测修复状态
        setTimeout(() => {
            const results = [];
            const options = document.querySelectorAll('.test-option');
            
            options.forEach((option, index) => {
                const price = option.querySelector('.project-price');
                const name = option.querySelector('.project-name');
                const content = option.querySelector('.el-option-content');
                
                // 检查元素是否存在
                const priceExists = price && price.textContent.trim() !== '';
                const nameExists = name && name.textContent.trim() !== '';
                
                // 检查是否可见
                const priceVisible = price && window.getComputedStyle(price).display !== 'none';
                const nameVisible = name && window.getComputedStyle(name).display !== 'none';
                
                // 检查布局
                const contentStyle = window.getComputedStyle(content);
                const isFlexLayout = contentStyle.display === 'flex';
                
                results.push({
                    index: index + 1,
                    priceExists,
                    nameExists,
                    priceVisible,
                    nameVisible,
                    isFlexLayout,
                    priceText: price ? price.textContent : '无',
                    nameText: name ? name.textContent.substring(0, 30) + '...' : '无'
                });
            });
            
            // 显示结果
            const resultsDiv = document.getElementById('test-results');
            let html = '<h3>🔍 检测结果：</h3>';
            
            results.forEach(result => {
                const allPass = result.priceExists && result.nameExists && 
                               result.priceVisible && result.nameVisible && 
                               result.isFlexLayout;
                
                html += `
                    <div class="check-item ${allPass ? 'check-pass' : 'check-fail'}">
                        <strong>选项 ${result.index}:</strong> ${allPass ? '✅ 通过' : '❌ 失败'}<br>
                        - 价格存在: ${result.priceExists ? '✅' : '❌'} (${result.priceText})<br>
                        - 名称存在: ${result.nameExists ? '✅' : '❌'} (${result.nameText})<br>
                        - 价格可见: ${result.priceVisible ? '✅' : '❌'}<br>
                        - 名称可见: ${result.nameVisible ? '✅' : '❌'}<br>
                        - Flex布局: ${result.isFlexLayout ? '✅' : '❌'}
                    </div>
                `;
            });
            
            const overallPass = results.every(r => 
                r.priceExists && r.nameExists && r.priceVisible && r.nameVisible && r.isFlexLayout
            );
            
            html += `
                <div class="check-item ${overallPass ? 'check-pass' : 'check-fail'}">
                    <strong>🎯 总体结果: ${overallPass ? '✅ 修复成功！' : '❌ 仍有问题'}</strong>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
            
            // 控制台输出详细信息
            console.log('=== 修复检测结果 ===');
            results.forEach(result => {
                console.log(`选项 ${result.index}:`, result);
            });
            console.log('总体状态:', overallPass ? '成功' : '失败');
            
        }, 1000);
    </script>
</body>
</html>
