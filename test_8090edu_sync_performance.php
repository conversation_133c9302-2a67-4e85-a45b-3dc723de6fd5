<?php
/**
 * 8090教育同步性能测试脚本
 * 对比分页获取 vs 一次性获取的性能差异
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/confing/common.php');

// 获取8090教育货源信息
function get8090eduSource() {
    global $DB;
    
    $sql = "SELECT * FROM qingka_wangke_huoyuan WHERE pt = '8090edu' OR name LIKE '%8090%' LIMIT 1";
    $result = $DB->get_row($sql);
    
    if (!$result) {
        echo "❌ 未找到8090教育货源配置\n";
        return false;
    }
    
    return $result;
}

// 获取认证Token
function getAuthToken($a) {
    $login_data = array(
        "username" => $a["user"],
        "password" => $a["pass"]
    );

    $login_url = "{$a["url"]}/api/auth/login";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    $login_result = curl_exec($ch);
    curl_close($ch);

    $login_result = json_decode($login_result, true);

    if (!$login_result || $login_result["code"] != 200) {
        return false;
    }

    return $login_result["data"]["token"];
}

// 测试一次性获取
function testSingleRequest($a, $token, $pageSize = 10000) {
    echo "[TEST] 测试一次性获取 (pageSize: {$pageSize})...\n";
    
    $start_time = microtime(true);
    $start_memory = memory_get_usage();
    
    $sites_url = "{$a["url"]}/api/price/sites?page=1&pageSize={$pageSize}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $sites_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $sites_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $end_time = microtime(true);
    $end_memory = memory_get_usage();
    
    $execution_time = round($end_time - $start_time, 2);
    $memory_used = round(($end_memory - $start_memory) / 1024 / 1024, 2);
    
    if ($curl_error) {
        return [
            'success' => false,
            'error' => "网络错误: {$curl_error}",
            'time' => $execution_time,
            'memory' => $memory_used
        ];
    }
    
    if ($http_code !== 200) {
        return [
            'success' => false,
            'error' => "HTTP错误: {$http_code}",
            'time' => $execution_time,
            'memory' => $memory_used
        ];
    }
    
    $sites_result = json_decode($sites_result, true);
    
    if (!$sites_result || $sites_result["code"] != 200) {
        $error_msg = isset($sites_result["message"]) ? $sites_result["message"] : "API响应异常";
        return [
            'success' => false,
            'error' => $error_msg,
            'time' => $execution_time,
            'memory' => $memory_used
        ];
    }
    
    $all_sites = isset($sites_result["data"]["list"]) ? $sites_result["data"]["list"] : [];
    $total_records = isset($sites_result["data"]["total"]) ? $sites_result["data"]["total"] : 0;
    
    return [
        'success' => true,
        'count' => count($all_sites),
        'total' => $total_records,
        'time' => $execution_time,
        'memory' => $memory_used,
        'complete' => count($all_sites) >= $total_records
    ];
}

// 测试分页获取
function testPaginationRequest($a, $token, $pageSize = 500) {
    echo "[TEST] 测试分页获取 (pageSize: {$pageSize})...\n";
    
    $start_time = microtime(true);
    $start_memory = memory_get_usage();
    
    $all_sites = [];
    $page = 1;
    $total_pages = 1;
    $request_count = 0;
    
    do {
        $sites_url = "{$a["url"]}/api/price/sites?page={$page}&pageSize={$pageSize}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $sites_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $sites_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        $request_count++;
        
        if ($curl_error) {
            $end_time = microtime(true);
            $end_memory = memory_get_usage();
            return [
                'success' => false,
                'error' => "网络错误: {$curl_error}",
                'time' => round($end_time - $start_time, 2),
                'memory' => round(($end_memory - $start_memory) / 1024 / 1024, 2),
                'requests' => $request_count
            ];
        }
        
        $sites_result = json_decode($sites_result, true);
        
        if (!$sites_result || $sites_result["code"] != 200) {
            $end_time = microtime(true);
            $end_memory = memory_get_usage();
            $error_msg = isset($sites_result["message"]) ? $sites_result["message"] : "API响应异常";
            return [
                'success' => false,
                'error' => $error_msg,
                'time' => round($end_time - $start_time, 2),
                'memory' => round(($end_memory - $start_memory) / 1024 / 1024, 2),
                'requests' => $request_count
            ];
        }
        
        if (isset($sites_result["data"]["list"])) {
            $all_sites = array_merge($all_sites, $sites_result["data"]["list"]);
            
            if ($page == 1 && isset($sites_result["data"]["total"])) {
                $total_records = $sites_result["data"]["total"];
                $total_pages = ceil($total_records / $pageSize);
            }
        }
        
        $page++;
        
        // 添加延迟避免请求过快
        if ($page <= $total_pages) {
            usleep(50000); // 0.05秒延迟
        }
        
    } while ($page <= $total_pages);
    
    $end_time = microtime(true);
    $end_memory = memory_get_usage();
    
    return [
        'success' => true,
        'count' => count($all_sites),
        'total' => $total_records ?? 0,
        'time' => round($end_time - $start_time, 2),
        'memory' => round(($end_memory - $start_memory) / 1024 / 1024, 2),
        'requests' => $request_count,
        'pages' => $total_pages
    ];
}

// 主测试程序
echo "🧪 8090教育同步性能测试\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";
echo "=" . str_repeat("=", 60) . "\n";

// 获取货源信息
$source = get8090eduSource();
if (!$source) {
    exit(1);
}

// 获取Token
echo "🔐 获取认证Token...\n";
$token = getAuthToken($source);
if (!$token) {
    echo "❌ Token获取失败\n";
    exit(1);
}
echo "✅ Token获取成功\n\n";

// 测试不同的一次性获取策略
$single_tests = [
    ['pageSize' => 50000, 'name' => '超大批量'],
    ['pageSize' => 10000, 'name' => '大批量'],
    ['pageSize' => 5000, 'name' => '中批量'],
    ['pageSize' => 1000, 'name' => '小批量']
];

echo "📊 一次性获取测试结果:\n";
echo "-" . str_repeat("-", 60) . "\n";

foreach ($single_tests as $test) {
    $result = testSingleRequest($source, $token, $test['pageSize']);
    
    echo sprintf("%-10s | ", $test['name']);
    
    if ($result['success']) {
        $complete_status = $result['complete'] ? '✅完整' : '⚠️截断';
        echo sprintf("成功 | %6d条 | %6.2fs | %5.1fMB | %s\n", 
            $result['count'], 
            $result['time'], 
            $result['memory'],
            $complete_status
        );
    } else {
        echo sprintf("失败 | %s | %6.2fs | %5.1fMB\n", 
            $result['error'], 
            $result['time'], 
            $result['memory']
        );
    }
}

echo "\n📊 分页获取测试结果:\n";
echo "-" . str_repeat("-", 60) . "\n";

// 测试分页获取
$pagination_result = testPaginationRequest($source, $token, 500);

if ($pagination_result['success']) {
    echo sprintf("分页获取   | 成功 | %6d条 | %6.2fs | %5.1fMB | %d请求 | %d页\n",
        $pagination_result['count'],
        $pagination_result['time'],
        $pagination_result['memory'],
        $pagination_result['requests'],
        $pagination_result['pages']
    );
} else {
    echo sprintf("分页获取   | 失败 | %s | %6.2fs | %5.1fMB | %d请求\n",
        $pagination_result['error'],
        $pagination_result['time'],
        $pagination_result['memory'],
        $pagination_result['requests']
    );
}

echo "\n🎯 性能对比分析:\n";
echo "=" . str_repeat("=", 60) . "\n";

// 找到最佳的一次性获取结果
$best_single = null;
foreach ($single_tests as $index => $test) {
    $result = testSingleRequest($source, $token, $test['pageSize']);
    if ($result['success'] && $result['complete']) {
        $best_single = $result;
        $best_single['name'] = $test['name'];
        break;
    }
}

if ($best_single && $pagination_result['success']) {
    $time_improvement = round((($pagination_result['time'] - $best_single['time']) / $pagination_result['time']) * 100, 1);
    $request_reduction = $pagination_result['requests'] - 1;
    
    echo "✅ 最佳一次性获取: {$best_single['name']} (pageSize: {$single_tests[0]['pageSize']})\n";
    echo "📈 性能提升:\n";
    echo "   ⏱️  时间节省: {$time_improvement}% ({$pagination_result['time']}s → {$best_single['time']}s)\n";
    echo "   📡 请求减少: {$request_reduction} 个 ({$pagination_result['requests']} → 1)\n";
    echo "   💾 内存对比: 分页 {$pagination_result['memory']}MB vs 一次性 {$best_single['memory']}MB\n";
    echo "   📊 数据完整性: " . ($best_single['complete'] ? '✅完整获取' : '⚠️可能截断') . "\n";
    
    if ($time_improvement > 0) {
        echo "\n🎉 结论: 一次性获取方式性能更优，建议使用！\n";
    } else {
        echo "\n⚖️  结论: 两种方式性能相近，建议根据实际情况选择\n";
    }
} else {
    echo "⚠️  无法找到有效的一次性获取方案，建议使用分页获取\n";
}

echo "\n💡 建议:\n";
echo "1. 如果API支持大pageSize且数据完整，优先使用一次性获取\n";
echo "2. 如果一次性获取有限制，使用智能分页策略\n";
echo "3. 定期监控API性能，根据实际情况调整策略\n";

echo "\n✅ 测试完成！\n";
?>
