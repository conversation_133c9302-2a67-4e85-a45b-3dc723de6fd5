<?php
/**
 * 8090教育优化查课逻辑
 * 解决两个关键问题：
 * 1. 查课失败时返回错误信息，不允许下单
 * 2. 根据check_course字段区分查课下单和无查下单
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

// 防止直接访问
if (!defined('IN_CRONLITE')) exit();

/**
 * 8090教育优化查课函数
 * @param string $user 学生账号
 * @param string $pass 学生密码  
 * @param string $noun 项目ID
 * @param array $a 货源信息
 * @return array 查课结果
 */
function check8090eduOptimized($user, $pass, $noun, $a) {
    global $DB;
    
    // 1. 首先获取项目的查课支持信息
    $project_info = getProjectCheckCourseInfo($noun, $a["hid"]);
    if (!$project_info) {
        return ['code' => -1, 'msg' => '项目信息获取失败', 'data' => []];
    }
    
    // 2. 根据check_course字段决定处理方式
    if ($project_info['check_course'] === '不支持') {
        // 无查下单：直接返回账号密码格式
        return handleNoCheckCourse($user, $pass, $project_info);
    } elseif ($project_info['check_course'] === '支持') {
        // 查课下单：执行查课逻辑
        return handleCheckCourse($user, $pass, $noun, $a, $project_info);
    } else {
        // 未知状态：尝试查课，失败则返回错误
        $result = handleCheckCourse($user, $pass, $noun, $a, $project_info);
        if ($result['code'] === -1) {
            // 查课失败且状态未知，返回错误不允许下单
            return ['code' => -1, 'msg' => '项目查课状态未知，查课失败：' . $result['msg'], 'data' => []];
        }
        return $result;
    }
}

/**
 * 获取项目查课支持信息
 * @param string $noun 项目ID
 * @param string $hid 货源ID
 * @return array|false 项目信息
 */
function getProjectCheckCourseInfo($noun, $hid) {
    global $DB;
    
    $escaped_noun = $DB->escape($noun);
    $escaped_hid = $DB->escape($hid);
    
    $sql = "SELECT name, check_course, exam_support, site_status, content 
            FROM qingka_wangke_class 
            WHERE docking = '{$escaped_hid}' AND noun = '{$escaped_noun}' AND status = 1 
            LIMIT 1";
    
    $result = $DB->get_row($sql);
    
    if (!$result) {
        return false;
    }
    
    // 如果check_course字段为空或未知，尝试从content字段解析
    if (empty($result['check_course']) || $result['check_course'] === '未知') {
        $result['check_course'] = parseCheckCourseFromContent($result['content']);
    }
    
    return $result;
}

/**
 * 从content字段解析查课支持信息
 * @param string $content 项目内容
 * @return string 查课支持状态
 */
function parseCheckCourseFromContent($content) {
    if (strpos($content, '项目查课: 支持') !== false) {
        return '支持';
    } elseif (strpos($content, '项目查课: 不支持') !== false) {
        return '不支持';
    }
    return '未知';
}

/**
 * 处理无查下单
 * @param string $user 学生账号
 * @param string $pass 学生密码
 * @param array $project_info 项目信息
 * @return array 结果
 */
function handleNoCheckCourse($user, $pass, $project_info) {
    // 检查网站状态
    if (isset($project_info['site_status']) && $project_info['site_status'] === '维护中') {
        return ['code' => -1, 'msg' => '网站维护中，暂时无法下单', 'data' => []];
    }
    
    // 返回账号密码格式，支持直接下单
    $account_info = $user . "----" . $pass;
    $json_data = [['name' => $account_info]];
    
    return [
        'code' => 0, 
        'msg' => '本项目无需查课，直接勾选下单', 
        'data' => $json_data,
        'mode' => 'no_check'
    ];
}

/**
 * 处理查课下单
 * @param string $user 学生账号
 * @param string $pass 学生密码
 * @param string $noun 项目ID
 * @param array $a 货源信息
 * @param array $project_info 项目信息
 * @return array 结果
 */
function handleCheckCourse($user, $pass, $noun, $a, $project_info) {
    // 1. Token验证和刷新
    $token_result = validateAndRefreshToken($a);
    if ($token_result['code'] !== 0) {
        return $token_result;
    }
    $token = $token_result['token'];
    
    // 2. 获取网站信息
    $site_info_result = getSiteInfo($noun, $a, $token);
    if ($site_info_result['code'] !== 0) {
        return $site_info_result;
    }
    $site_name = $site_info_result['site_name'];
    
    // 3. 执行查课
    $query_result = performCourseQuery($user, $pass, $site_name);
    if ($query_result['code'] !== 0) {
        return $query_result;
    }
    
    // 4. 解析课程数据
    $courses = parseCourseData($query_result['data']);
    if (empty($courses)) {
        return ['code' => -1, 'msg' => '未查询到课程信息', 'data' => []];
    }
    
    return [
        'code' => 0, 
        'msg' => '查课成功', 
        'data' => $courses,
        'mode' => 'check_course'
    ];
}

/**
 * 验证和刷新Token
 * @param array $a 货源信息
 * @return array 结果
 */
function validateAndRefreshToken($a) {
    global $DB;
    
    $token = $a["token"];
    $need_refresh_token = false;
    
    // 检查Token是否为空
    if (empty($token)) {
        $need_refresh_token = true;
    } else {
        // 验证Token有效性
        $test_url = "{$a["url"]}/api/user/balance";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $test_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            return ['code' => -1, 'msg' => "Token验证网络错误: " . $curl_error, 'token' => null];
        }
        
        $test_result_array = json_decode($test_result, true);
        if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
            $need_refresh_token = true;
        }
    }
    
    // 刷新Token
    if ($need_refresh_token) {
        $refresh_result = refreshToken($a);
        if ($refresh_result['code'] !== 0) {
            return $refresh_result;
        }
        $token = $refresh_result['token'];
    }
    
    return ['code' => 0, 'msg' => 'Token验证成功', 'token' => $token];
}

/**
 * 刷新Token
 * @param array $a 货源信息
 * @return array 结果
 */
function refreshToken($a) {
    global $DB;
    
    $login_url = "{$a["url"]}/api/auth/login";
    $login_data = array(
        "username" => $a["user"],
        "password" => $a["pass"]
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $login_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['code' => -1, 'msg' => "Token刷新网络错误: " . $curl_error, 'token' => null];
    }
    
    $login_result_array = json_decode($login_result, true);
    if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
        $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
        return ['code' => -1, 'msg' => "Token刷新失败: " . $error_msg, 'token' => null];
    }
    
    $new_token = $login_result_array["data"]["token"];
    
    // 更新数据库中的Token
    $escaped_token = $DB->escape($new_token);
    $escaped_hid = $DB->escape($a["hid"]);
    $update_sql = "UPDATE qingka_wangke_huoyuan SET token = '{$escaped_token}', endtime = NOW() WHERE hid = '{$escaped_hid}'";
    $DB->query($update_sql);
    
    return ['code' => 0, 'msg' => 'Token刷新成功', 'token' => $new_token];
}

/**
 * 获取网站信息
 * @param string $noun 项目ID
 * @param array $a 货源信息
 * @param string $token Token
 * @return array 结果
 */
function getSiteInfo($noun, $a, $token) {
    $site_url = "{$a["url"]}/api/order/website/info?websiteId={$noun}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $site_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $site_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['code' => -1, 'msg' => "获取网站信息网络错误: " . $curl_error, 'site_name' => null];
    }
    
    $site_result_array = json_decode($site_result, true);
    if (!$site_result_array || !isset($site_result_array["code"]) || $site_result_array["code"] != 200) {
        $error_msg = isset($site_result_array["message"]) ? $site_result_array["message"] : "获取网站信息失败";
        return ['code' => -1, 'msg' => $error_msg, 'site_name' => null];
    }
    
    $site_name = isset($site_result_array["data"]["site_name"]) ? $site_result_array["data"]["site_name"] : "";
    if (empty($site_name)) {
        return ['code' => -1, 'msg' => '无法获取网站名称', 'site_name' => null];
    }
    
    return ['code' => 0, 'msg' => '获取网站信息成功', 'site_name' => $site_name];
}

/**
 * 执行课程查询
 * @param string $user 学生账号
 * @param string $pass 学生密码
 * @param string $site_name 网站名称
 * @return array 结果
 */
function performCourseQuery($user, $pass, $site_name) {
    $query_url = "http://1.14.58.242:15888/query";
    $query_params = array(
        "username" => $user,
        "password" => $pass,
        "courseName" => $site_name,
        "Time" => time() * 1000
    );
    
    $query_url_with_params = $query_url . "?" . http_build_query($query_params);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $query_url_with_params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $query_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['code' => -1, 'msg' => "查课请求失败: " . $curl_error, 'data' => null];
    }
    
    $query_result_array = json_decode($query_result, true);
    if (!$query_result_array || !isset($query_result_array["code"])) {
        return ['code' => -1, 'msg' => '查课响应格式错误', 'data' => null];
    }
    
    if ($query_result_array["code"] != 200) {
        $error_msg = isset($query_result_array["message"]) ? $query_result_array["message"] : "查课失败";
        return ['code' => -1, 'msg' => $error_msg, 'data' => null];
    }
    
    return ['code' => 0, 'msg' => '查课请求成功', 'data' => $query_result_array];
}

/**
 * 解析课程数据
 * @param array $query_data 查课返回数据
 * @return array 课程列表
 */
function parseCourseData($query_data) {
    $json_data = [];
    
    // 处理多课程情况
    if (isset($query_data["data"]["children"]) && is_array($query_data["data"]["children"])) {
        foreach ($query_data["data"]["children"] as $course) {
            if (isset($course["name"]) && !empty($course["name"])) {
                $json_data[] = ['name' => $course["name"]];
            }
        }
    }
    // 处理单课程情况
    elseif (isset($query_data["data"]["name"]) && !empty($query_data["data"]["name"])) {
        $json_data[] = ['name' => $query_data["data"]["name"]];
    }
    
    return $json_data;
}

/**
 * 主查课函数（兼容原有接口）
 * @param string $user 学生账号
 * @param string $pass 学生密码
 * @param string $noun 项目ID
 * @param array $a 货源信息
 * @return array 查课结果
 */
function ckjk8090edu($user, $pass, $noun, $a) {
    return check8090eduOptimized($user, $pass, $noun, $a);
}

?>
