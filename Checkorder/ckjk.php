<?php 
function getMillisecond() { 

    list($t1, $t2) = explode(' ', microtime()); 
  
    return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000); 
  
}
// 查课接口设置
function getWk($type, $noun, $school, $user, $pass, $name = false){
	global $DB;
	global $wk;
	$huoyuan_id = $type; // 保存原始的货源ID
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$type}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	
    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=get";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }

    //YYY查课接口
    if ($type == "yyy") {

        $maxAttempts = 20; // 最大尝试次数
        $intervalTime = 2; // 尝试间隔时间（秒）
        $attempt = 0;
        $json_data = [];

        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "search" => 1);
        //die(json_encode($data));
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api/order";

        while ($attempt < $maxAttempts) {

            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);

            if (isset($result['data']) && is_array($result['data']) ) {

                if (count($result['data']) > 0){
                    foreach ($result['data'] as $row) {
                        $json_data[] = ['name' => $row];
                    }
                    break;
                }elseif ($result['message']!=='查询成功'){
                    return ['code' => -1, 'msg' => $result['message'], 'data' => []];
                }
            }

            $attempt++;
            if ($attempt < $maxAttempts) {
                sleep($intervalTime);
            }
        }

        if ($attempt == $maxAttempts && empty($json_data)) {
            return ['code' => -1, 'msg' => '查询中，请稍后尝试', 'data' => []];
        }

        $b  = ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
        return $b;
    }

       //暗网查课
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=get";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }

    //8090edu优化查课接口
    if ($type == "8090edu") {
        // 引入优化的查课逻辑
        require_once("8090edu_optimized.php");

        // 调用优化的查课函数
        $result = ckjk8090edu($user, $pass, $noun, $a);
        return $result;
    }

    //易教育查课接口
    if ($type == "jxjy") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效 - 通过调用用户信息接口来测试
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || empty($test_result)) {
                $need_refresh_token = true;
            } else {
                $test_data = json_decode($test_result, true);
                if (!$test_data || $test_data["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，则重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || empty($login_result)) {
                return ['code' => -1, 'msg' => '登录失败：网络错误', 'data' => []];
            }

            $login_data = json_decode($login_result, true);
            if (!$login_data || $login_data["code"] != 200) {
                $error_msg = isset($login_data["message"]) ? $login_data["message"] : "登录失败";
                return ['code' => -1, 'msg' => $error_msg, 'data' => []];
            }

            $token = $login_data["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$type}'");
        }

        // 获取项目信息 - 从主商品表查询，使用noun字段匹配项目编号
        $class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' AND docking = '{$huoyuan_id}' LIMIT 1");
        if (!$class_info) {
            return ['code' => -1, 'msg' => '项目信息不存在，项目编号: ' . $noun . '，货源ID: ' . $huoyuan_id, 'data' => []];
        }

        $websiteNumber = $class_info['noun']; // 使用noun字段作为项目编号

        // 判断易教育项目是否需要查课
        // 可以通过项目名称、内容描述或其他字段来判断
        $project_name = strtolower($class_info['name']);
        $project_content = strtolower($class_info['content']);

        // 无需查课的关键词列表
        $no_search_keywords = array(
            '无需查课', '直接下单', '免查课', '不用查课',
            '无查课', '直接提交', '免查', '秒过',
            'nosearch', 'direct', '直接'
        );

        $isSearchCourse = '1'; // 默认需要查课

        // 检查项目名称和内容是否包含无需查课的关键词
        foreach ($no_search_keywords as $keyword) {
            if (strpos($project_name, $keyword) !== false || strpos($project_content, $keyword) !== false) {
                $isSearchCourse = '0';
                break;
            }
        }

        // 也可以通过项目编号的特定规则来判断（如果有的话）
        // 例如：某些特定编号范围的项目无需查课
        // if (strpos($websiteNumber, 'direct') !== false) {
        //     $isSearchCourse = '0';
        // }

        // 如果项目不支持查课，直接返回账号密码信息
        if ($isSearchCourse == '0') {
            $account_info = $user . "----" . $pass;
            $json_data[] = ['name' => $account_info];
            return ['code' => 0, 'msg' => '该项目无需查课，可直接下单', 'data' => $json_data];
        }

        // 发起查课请求
        $query_data = array(
            "websiteNumber" => $websiteNumber,
            "data" => array(array("username" => $user, "password" => $pass))
        );

        $query_url = "{$a["url"]}/api/website/queryCourse";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        $query_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || empty($query_result)) {
            return ['code' => -1, 'msg' => '查课请求失败：网络错误', 'data' => []];
        }

        $query_data = json_decode($query_result, true);
        if (!$query_data || $query_data["code"] != 200) {
            $error_msg = isset($query_data["message"]) ? $query_data["message"] : "查课请求失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        $uuid = $query_data['data']['uuid'];

        // 等待5秒后获取查课结果
        sleep(5);

        $result_data = array("uuid" => $uuid);
        $result_url = "{$a["url"]}/api/website/getQueryCourse";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $result_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($result_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        $result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || empty($result)) {
            return ['code' => -1, 'msg' => '获取查课结果失败：网络错误', 'data' => []];
        }

        $result_data = json_decode($result, true);
        if (!$result_data || $result_data["code"] != 200) {
            $error_msg = isset($result_data["message"]) ? $result_data["message"] : "获取查课结果失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        // 检查是否登录失败
        if (isset($result_data['data'][0]['name']) && strpos($result_data['data'][0]['name'], "登录失败") !== false) {
            return ['code' => -1, 'msg' => $result_data['data'][0]['name'], 'data' => []];
        }

        $json_data = [];

        // 解析课程信息
        if (isset($result_data['data'][0]['children'])) {
            $children = $result_data['data'][0]['children'];
            foreach ($children as $child) {
                if (isset($child['id']) && isset($child['name'])) {
                    $json_data[] = array("name" => $child['name']);
                } elseif (isset($child['name']) && isset($child['children'])) {
                    // 处理嵌套的课程结构
                    foreach ($child['children'] as $subChild) {
                        if (isset($subChild['id']) && isset($subChild['name'])) {
                            $course_name = $child['name'] . "----" . $subChild['name'];
                            $json_data[] = array("name" => $course_name);
                        }
                    }
                }
            }
        }

        if (empty($json_data)) {
            return ['code' => -1, 'msg' => '未找到可选课程', 'data' => []];
        }

        return ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
    }

	else {
    print_r("没有了,文件ckjk.php,可能故障：参数缺少，比如平台名错误！！！");die;
	}
}
 

?>