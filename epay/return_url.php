<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");

$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyReturn();

if($verify_result) {
	$out_trade_no = $_GET['out_trade_no'];
	$out_trade_no = preg_replace('/[^0-9]/', '', $out_trade_no);
	$type = $_POST['type'];
	$trade_no = $_GET['trade_no'];
	$trade_status = $_GET['trade_status'];
	$type = $_GET['type'];
	$money=$_GET['money'];
	$money3=0;
	/*if ($money<50) {
	    $money3=0;
	}
	if ($money>=50) {
	    $money3=$money*0.02;
	}
	if ($money>=100) {
	    $money3=$money*0.05;
	}
	if ($money>=300) {
	    $money3=$money*0.08;
	}
	if ($money>=500) {
	    $money3=$money*0.10;
	}*/
	$sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? limit 1 for update";
	$srow = $DB->prepare_getrow($sql, [$out_trade_no]);

	$sql = "SELECT * FROM qingka_wangke_user WHERE uid=?";
	$userrow=$DB->prepare_getrow($sql, [$srow['uid']]);

    $money1= $userrow['money'];
	$money2 = $money1+$money+$money3;
	if ($money3!=0) {
	    $cg="用户[{$userrow['user']}]成功充值".$money."积分；本次赠送".$money3."积分! ";
	}else {
	    $cg="用户[{$userrow['user']}]成功充值".$money."积分! ";
	}
    if($_GET['trade_status'] == 'TRADE_FINISHED' || $_GET['trade_status'] == 'TRADE_SUCCESS') {
		if($srow['status']==0 && $srow['money']==$money){
			$sql = "update `qingka_wangke_pay` set `status` ='1',`endtime` =?,`trade_no`=? where `out_trade_no`=?";
			$DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
            $sql = "update `qingka_wangke_user` set `money`=?,`zcz`=zcz+? where uid=?";
			$DB->prepare_query($sql, [$money2, $money, $srow['uid']]);
			wlog($userrow['uid'],"在线充值","用户[{$userrow['user']}]在线充值了{$money}积分",$money);
			if ($money3!=0) {
           	    wlog($userrow['uid'],"在线充值","用户[{$userrow['user']}]充值金额达标赠送{$money3}积分",$money3);
           	}
			exit("<script language='javascript'>alert('$cg');window.location.href='../index/pay';</script>");
		}else{
		    // 检查订单是否已经处理过
		    if($srow['status']==1){
		        // 订单已处理，这是正常的重复访问
		        exit("<script language='javascript'>alert('订单已处理完成');window.location.href='../index/pay';</script>");
		    } else {
		        // 订单未处理但金额不匹配，记录异常
		        $sql = "update `qingka_wangke_pay` set `status` ='1',`endtime` =?,`trade_no`=? where `out_trade_no`=?";
		        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
		        wlog($userrow['uid'],"在线充值","充值异常--订单金额不匹配，订单金额:{$srow['money']},支付金额:{$money}",0);
		        exit("<script language='javascript'>alert('充值异常，请联系客服');window.location.href='../index/pay';</script>");
		    }
		}
    }
    else {
      echo "trade_status=".$_GET['trade_status'];
    }
}
 else {
	   exit("<script language='javascript'>alert('充值失败!');window.location.href='../index/index';</script>");
}
?>