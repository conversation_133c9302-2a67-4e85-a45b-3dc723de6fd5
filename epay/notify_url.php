<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");
$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();

if($verify_result) {
	$out_trade_no = $_GET['out_trade_no'];
	$trade_no = $_GET['trade_no'];
	$trade_status = $_GET['trade_status'];
    $money=$_GET['money'];
	$type = $_GET['type'];

    // 使用行锁防止并发问题
    $sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? LIMIT 1 FOR UPDATE";
    $srow = $DB->prepare_getrow($sql, [$out_trade_no]);

    if (!$srow) {
        echo "fail"; // 订单不存在
        exit;
    }

    if ($_GET['trade_status'] == 'TRADE_SUCCESS' && $srow['status']==0 && $srow['money']==$money) {
        // 开始事务
        $DB->query('BEGIN');
        try {
            // 原子性更新订单状态
            $sql = "UPDATE `qingka_wangke_pay` SET `status` = '1', `endtime` = ?, `trade_no` = ? WHERE `out_trade_no` = ? AND `status` = '0'";
            $result = $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);

            if ($result && $DB->affected_rows() > 0) {
                // 更新用户余额
                $sql = "SELECT * FROM qingka_wangke_user WHERE uid=?";
                $userrow = $DB->prepare_getrow($sql, [$srow['uid']]);

                $money3 = 0; // 赠送金额逻辑
                $money2 = $userrow['money'] + $money + $money3;

                $sql = "UPDATE `qingka_wangke_user` SET `money`=?, `zcz`=zcz+? WHERE uid=?";
                $user_result = $DB->prepare_query($sql, [$money2, $money, $srow['uid']]);

                if ($user_result) {
                    $DB->query('COMMIT');
                    wlog($srow['uid'], "在线充值", "notify回调-用户[{$userrow['user']}]在线充值了{$money}积分", $money);
                    echo "success";
                } else {
                    $DB->query('ROLLBACK');
                    echo "fail";
                }
            } else {
                $DB->query('ROLLBACK');
                echo "success"; // 订单已处理，返回成功避免重复回调
            }
        } catch (Exception $e) {
            $DB->query('ROLLBACK');
            echo "fail";
        }
    } else {
        // 更新时间戳但不改变状态
        $sql = "UPDATE `qingka_wangke_pay` SET `endtime` = ?, `trade_no` = ? WHERE `out_trade_no` = ?";
        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
        echo "success";
    }
}
else {
	echo "fail";
}
?>