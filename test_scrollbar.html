<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动条测试</title>
    <link rel="stylesheet" href="assets/css/select-optimization.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-option {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 10px;
            background: white;
        }
        .test-option:hover {
            background: #f8f9fa;
        }
        .el-option-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .project-price {
            flex-shrink: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            min-width: 70px;
            text-align: center;
        }
        .project-name {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            line-height: 1.4;
            
            /* 强制显示滚动条 */
            white-space: nowrap;
            overflow-x: scroll;
            overflow-y: hidden;
            max-width: 300px;
            min-height: 20px;
            padding: 2px 0 8px 0;
            
            /* Firefox */
            scrollbar-width: thin;
            scrollbar-color: #bbb #f0f0f0;
        }
        .project-name::-webkit-scrollbar {
            height: 8px;
        }
        .project-name::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .project-name::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            border: 1px solid #f1f1f1;
        }
        .project-name::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .instruction {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0050b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>滚动条测试页面</h1>
        
        <div class="instruction">
            <strong>测试说明：</strong><br>
            1. 下面的项目名称应该显示水平滚动条<br>
            2. 可以用鼠标拖拽滚动条或在文本上滑动<br>
            3. 移动端可以用手指滑动
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">50积分</span>
                <div class="project-name">这是一个非常非常非常长的项目名称，用来测试水平滚动条是否正常显示和工作</div>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">100积分</span>
                <div class="project-name">超级长的项目名称测试案例，包含很多很多很多文字内容，需要滚动才能看完整</div>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">200积分</span>
                <div class="project-name">短名称</div>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">300积分</span>
                <div class="project-name">这个项目名称也很长很长很长很长很长很长很长很长很长很长很长很长很长很长</div>
            </div>
        </div>

        <div class="test-option el-option">
            <div class="el-option-content">
                <span class="project-price">500积分</span>
                <div class="project-name">测试项目：包含中文、English、数字123、特殊符号@#$%，以及更多更多更多更多内容</div>
            </div>
        </div>
    </div>

    <script>
        // 添加拖拽滚动功能
        document.querySelectorAll('.project-name').forEach(function(element) {
            let isScrolling = false;
            let startX = 0;
            let scrollLeft = 0;
            
            element.addEventListener('mousedown', function(e) {
                if (element.scrollWidth <= element.clientWidth) return;
                
                isScrolling = true;
                startX = e.pageX - element.offsetLeft;
                scrollLeft = element.scrollLeft;
                element.style.cursor = 'grabbing';
                e.preventDefault();
            });
            
            element.addEventListener('mousemove', function(e) {
                if (!isScrolling) return;
                
                const x = e.pageX - element.offsetLeft;
                const walk = (x - startX) * 2;
                element.scrollLeft = scrollLeft - walk;
                e.preventDefault();
            });
            
            element.addEventListener('mouseup', function() {
                isScrolling = false;
                element.style.cursor = 'grab';
            });
            
            element.addEventListener('mouseleave', function() {
                isScrolling = false;
                element.style.cursor = 'grab';
            });
            
            // 设置初始光标
            if (element.scrollWidth > element.clientWidth) {
                element.style.cursor = 'grab';
                element.title = '可以拖拽滚动查看完整内容';
            }
        });
    </script>
</body>
</html>
