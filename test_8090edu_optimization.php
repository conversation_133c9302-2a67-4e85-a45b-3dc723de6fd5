<?php
/**
 * 8090教育优化功能测试脚本
 * 测试查课/无查下单的区分逻辑
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

// 引入必要的文件
require_once('confing/common.php');
require_once('Checkorder/8090edu_optimized.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试数据
$test_cases = [
    [
        'name' => '支持查课的项目测试',
        'user' => 'test_user_1',
        'pass' => 'test_pass_1',
        'noun' => '100579', // 假设这是一个支持查课的项目ID
        'expected_mode' => 'check_course'
    ],
    [
        'name' => '不支持查课的项目测试',
        'user' => 'test_user_2', 
        'pass' => 'test_pass_2',
        'noun' => '100580', // 假设这是一个不支持查课的项目ID
        'expected_mode' => 'no_check'
    ],
    [
        'name' => '未知查课状态项目测试',
        'user' => 'test_user_3',
        'pass' => 'test_pass_3', 
        'noun' => '100581', // 假设这是一个未知状态的项目ID
        'expected_mode' => 'unknown'
    ]
];

// 获取8090教育货源信息
function get8090eduSource() {
    global $DB;
    
    $sql = "SELECT * FROM qingka_wangke_huoyuan WHERE pt = '8090edu' AND status = 1 LIMIT 1";
    $result = $DB->get_row($sql);
    
    if (!$result) {
        echo "❌ 未找到8090教育货源配置\n";
        return false;
    }
    
    return $result;
}

// 创建测试项目数据
function createTestProjects() {
    global $DB;
    
    $source = get8090eduSource();
    if (!$source) {
        return false;
    }
    
    $test_projects = [
        [
            'noun' => '100579',
            'name' => '测试支持查课项目',
            'check_course' => '支持',
            'exam_support' => '支持',
            'site_status' => '正常'
        ],
        [
            'noun' => '100580', 
            'name' => '测试不支持查课项目',
            'check_course' => '不支持',
            'exam_support' => '不支持',
            'site_status' => '正常'
        ],
        [
            'noun' => '100581',
            'name' => '测试未知查课状态项目', 
            'check_course' => '未知',
            'exam_support' => '未知',
            'site_status' => '正常'
        ]
    ];
    
    foreach ($test_projects as $project) {
        // 检查是否已存在
        $existing = $DB->get_row("SELECT cid FROM qingka_wangke_class WHERE docking = '{$source['hid']}' AND noun = '{$project['noun']}'");
        
        if (!$existing) {
            // 插入测试项目
            $sql = "INSERT INTO qingka_wangke_class 
                    (docking, name, noun, price, check_course, exam_support, site_status, content, status, addtime) 
                    VALUES 
                    ('{$source['hid']}', '{$project['name']}', '{$project['noun']}', 10, 
                     '{$project['check_course']}', '{$project['exam_support']}', '{$project['site_status']}', 
                     '测试项目 | 项目查课: {$project['check_course']} | 项目考试: {$project['exam_support']}', 
                     1, NOW())";
            
            if ($DB->query($sql)) {
                echo "✅ 创建测试项目: {$project['name']} (查课: {$project['check_course']})\n";
            } else {
                echo "❌ 创建测试项目失败: {$project['name']}\n";
            }
        } else {
            // 更新现有项目
            $sql = "UPDATE qingka_wangke_class 
                    SET check_course = '{$project['check_course']}', 
                        exam_support = '{$project['exam_support']}', 
                        site_status = '{$project['site_status']}',
                        name = '{$project['name']}'
                    WHERE docking = '{$source['hid']}' AND noun = '{$project['noun']}'";
            
            if ($DB->query($sql)) {
                echo "✅ 更新测试项目: {$project['name']} (查课: {$project['check_course']})\n";
            }
        }
    }
    
    return true;
}

// 运行测试
function runTests() {
    global $test_cases;
    
    $source = get8090eduSource();
    if (!$source) {
        return;
    }
    
    echo "\n🚀 开始8090教育优化功能测试\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    foreach ($test_cases as $index => $test_case) {
        echo "\n📋 测试案例 " . ($index + 1) . ": {$test_case['name']}\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        // 调用优化的查课函数
        $result = check8090eduOptimized(
            $test_case['user'], 
            $test_case['pass'], 
            $test_case['noun'], 
            $source
        );
        
        // 显示结果
        echo "📤 输入参数:\n";
        echo "   用户: {$test_case['user']}\n";
        echo "   密码: {$test_case['pass']}\n";
        echo "   项目ID: {$test_case['noun']}\n";
        
        echo "\n📥 返回结果:\n";
        echo "   状态码: {$result['code']}\n";
        echo "   消息: {$result['msg']}\n";
        echo "   数据数量: " . count($result['data']) . "\n";
        
        if (isset($result['mode'])) {
            echo "   处理模式: {$result['mode']}\n";
        }
        
        // 显示数据内容
        if (!empty($result['data'])) {
            echo "   数据内容:\n";
            foreach ($result['data'] as $item) {
                echo "     - {$item['name']}\n";
            }
        }
        
        // 分析结果
        analyzeResult($result, $test_case);
    }
    
    echo "\n🎯 测试完成\n";
}

// 分析测试结果
function analyzeResult($result, $test_case) {
    echo "\n🔍 结果分析:\n";
    
    if ($result['code'] === 0) {
        echo "   ✅ 查课请求成功\n";
        
        if (isset($result['mode'])) {
            switch ($result['mode']) {
                case 'no_check':
                    echo "   📝 无查下单模式 - 返回账号密码格式\n";
                    if (!empty($result['data']) && strpos($result['data'][0]['name'], '----') !== false) {
                        echo "   ✅ 账号密码格式正确\n";
                    } else {
                        echo "   ❌ 账号密码格式错误\n";
                    }
                    break;
                    
                case 'check_course':
                    echo "   🔍 查课下单模式 - 返回课程列表\n";
                    if (!empty($result['data'])) {
                        echo "   ✅ 返回了课程数据\n";
                    } else {
                        echo "   ⚠️  未返回课程数据\n";
                    }
                    break;
                    
                default:
                    echo "   ❓ 未知处理模式: {$result['mode']}\n";
            }
        }
    } else {
        echo "   ❌ 查课请求失败: {$result['msg']}\n";
        
        // 对于查课失败的情况，检查是否正确阻止了下单
        if ($test_case['expected_mode'] === 'unknown') {
            echo "   ✅ 未知状态项目查课失败时正确阻止下单\n";
        }
    }
}

// 显示项目查课状态统计
function showProjectStats() {
    global $DB;
    
    echo "\n📊 8090教育项目查课状态统计\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
    $sql = "SELECT 
                c.check_course,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (
                    SELECT COUNT(*) 
                    FROM qingka_wangke_class c2 
                    JOIN qingka_wangke_huoyuan h2 ON c2.docking = h2.hid 
                    WHERE h2.pt = '8090edu' AND c2.status = 1
                ), 2) as percentage
            FROM qingka_wangke_class c
            JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
            WHERE h.pt = '8090edu' AND c.status = 1
            GROUP BY c.check_course
            ORDER BY count DESC";
    
    $result = $DB->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $status_icon = '';
            switch ($row['check_course']) {
                case '支持':
                    $status_icon = '🔍';
                    break;
                case '不支持':
                    $status_icon = '📝';
                    break;
                default:
                    $status_icon = '❓';
            }
            
            echo sprintf("   %s %s: %d 个项目 (%.2f%%)\n", 
                $status_icon, 
                $row['check_course'], 
                $row['count'], 
                $row['percentage']
            );
        }
    } else {
        echo "   ❌ 未找到8090教育项目数据\n";
    }
}

// 清理测试数据
function cleanupTestData() {
    global $DB;
    
    $source = get8090eduSource();
    if (!$source) {
        return;
    }
    
    $test_nouns = ['100579', '100580', '100581'];
    
    foreach ($test_nouns as $noun) {
        $sql = "DELETE FROM qingka_wangke_class WHERE docking = '{$source['hid']}' AND noun = '{$noun}'";
        $DB->query($sql);
    }
    
    echo "\n🧹 测试数据清理完成\n";
}

// 主程序
echo "🎯 8090教育查课优化功能测试\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";

// 检查数据库连接
if (!$DB) {
    echo "❌ 数据库连接失败\n";
    exit(1);
}

// 显示当前项目统计
showProjectStats();

// 创建测试项目
echo "\n📝 准备测试数据...\n";
if (!createTestProjects()) {
    echo "❌ 测试数据准备失败\n";
    exit(1);
}

// 运行测试
runTests();

// 再次显示统计（包含测试数据）
showProjectStats();

// 询问是否清理测试数据
echo "\n❓ 是否清理测试数据？(y/n): ";
$handle = fopen("php://stdin", "r");
$input = trim(fgets($handle));
fclose($handle);

if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
    cleanupTestData();
} else {
    echo "📌 测试数据已保留，可用于进一步测试\n";
}

echo "\n✅ 测试脚本执行完成\n";
?>
