# 分类ID和名称显示优化

## 优化内容

现在系统会同时显示分类ID和分类名称，提供更完整的分类信息。

## 显示格式

### 1. 分类标题显示
```
ID:1 - 继续教育 (共 15 个商品)
ID:2 - 职业培训 (共 8 个商品)
ID:3 - 学历提升 (共 12 个商品)
```

### 2. 商品表格中的分类列（仅29系统显示）
```
| 商品名 | CID | 分类信息 | 价格 |
|--------|-----|----------|------|
| 商品A  | 123 | ID:1 - 继续教育 | 100 |
| 商品B  | 124 | ID:2 - 职业培训 | 200 |
```

### 3. 下拉选择框显示
```
ID:1 - 继续教育 (共 15 个商品)
ID:2 - 职业培训 (共 8 个商品)
ID:3 - 学历提升 (共 12 个商品)
```

## 数据字段说明

### 后端返回的商品数据结构
```json
{
  "cid": 123,
  "name": "商品名称",
  "fenlei": 1,                    // 原始分类ID字段
  "fenleiname": "继续教育",        // 原始分类名称字段（如果有）
  "fenlei_id": 1,                 // 处理后的分类ID
  "fenlei_name": "继续教育",       // 处理后的分类名称
  "fenlei_display": "ID:1 - 继续教育", // 组合显示格式
  "price": 100
}
```

## 分类名称获取优先级

1. **第一优先级**：从分类API获取的名称（通过分类ID映射）
2. **第二优先级**：商品数据中的 `fenleiname` 字段
3. **第三优先级**：显示"未知分类"

## 兼容性

- ✅ **29系统**：显示完整的"ID:X - 分类名称"格式
- ✅ **其他系统**：仍然显示"分类ID: X"格式，不受影响
- ✅ **向后兼容**：保持原有的 `fenlei` 字段不变

## 测试功能增强

测试API功能现在会显示：
```
示例分类:
  • ID:1 - 继续教育
  • ID:2 - 职业培训
  • ID:3 - 学历提升
```

## 使用场景

1. **商品管理**：快速识别商品所属分类
2. **批量操作**：按分类ID进行精确操作
3. **数据分析**：同时了解分类编号和含义
4. **问题排查**：通过ID和名称双重确认分类信息

## 技术实现

### 后端处理
- 同时保存 `fenlei_id`、`fenlei_name` 和 `fenlei_display`
- 智能获取分类名称（API > 商品字段 > 默认值）

### 前端显示
- 分类标题使用 `fenlei_display` 字段
- 商品表格新增分类信息列
- 下拉选择框显示完整格式

这样的设计既保持了向后兼容性，又提供了更丰富的分类信息显示。
