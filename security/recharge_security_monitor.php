<?php
/**
 * 充值安全监控和防护系统
 * 监控异常充值行为，防止重复请求攻击
 */

include(__DIR__ . '/../confing/common.php');

// 安全配置
$security_config = [
    'max_requests_per_minute' => 10,  // 每分钟最大请求次数
    'max_requests_per_hour' => 50,    // 每小时最大请求次数
    'suspicious_threshold' => 5,       // 可疑行为阈值
    'block_duration' => 300,          // 封禁时长（秒）
    'log_retention_days' => 30        // 日志保留天数
];

/**
 * 检查IP请求频率
 */
function checkRequestFrequency($ip, $action = 'recharge') {
    global $DB, $security_config;
    
    $current_time = time();
    $minute_ago = $current_time - 60;
    $hour_ago = $current_time - 3600;
    
    // 检查每分钟请求次数
    $minute_count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log 
                               WHERE ip = ? AND action = ? AND timestamp > ?", 
                               [$ip, $action, $minute_ago]);
    
    if ($minute_count >= $security_config['max_requests_per_minute']) {
        return ['blocked' => true, 'reason' => '请求过于频繁，请稍后再试'];
    }
    
    // 检查每小时请求次数
    $hour_count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log 
                             WHERE ip = ? AND action = ? AND timestamp > ?", 
                             [$ip, $action, $hour_ago]);
    
    if ($hour_count >= $security_config['max_requests_per_hour']) {
        return ['blocked' => true, 'reason' => '请求次数超限，请1小时后再试'];
    }
    
    return ['blocked' => false];
}

/**
 * 记录安全日志
 */
function logSecurityEvent($ip, $uid, $action, $details, $risk_level = 'low') {
    global $DB;
    
    $sql = "INSERT INTO qingka_wangke_security_log (ip, uid, action, details, risk_level, timestamp) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $params = [$ip, $uid, $action, $details, $risk_level, time()];
    $DB->prepare_query($sql, $params);
}

/**
 * 检查订单重复处理
 */
function checkOrderDuplication($out_trade_no) {
    global $DB;
    
    // 检查是否有重复的处理记录
    $count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log 
                        WHERE action = 'recharge_process' AND details LIKE ?", 
                        ["%{$out_trade_no}%"]);
    
    return $count > 0;
}

/**
 * 创建安全日志表
 */
function createSecurityLogTable() {
    global $DB;
    
    $sql = "CREATE TABLE IF NOT EXISTS `qingka_wangke_security_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `ip` varchar(45) NOT NULL,
        `uid` int(11) DEFAULT NULL,
        `action` varchar(50) NOT NULL,
        `details` text,
        `risk_level` enum('low','medium','high') DEFAULT 'low',
        `timestamp` int(11) NOT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_ip_action` (`ip`, `action`),
        KEY `idx_timestamp` (`timestamp`),
        KEY `idx_uid` (`uid`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表'";
    
    $DB->query($sql);
}

/**
 * 清理过期日志
 */
function cleanupOldLogs() {
    global $DB, $security_config;
    
    $expire_time = time() - ($security_config['log_retention_days'] * 24 * 3600);
    
    $sql = "DELETE FROM qingka_wangke_security_log WHERE timestamp < ?";
    $DB->prepare_query($sql, [$expire_time]);
}

/**
 * 检查可疑充值行为
 */
function detectSuspiciousActivity($uid, $amount) {
    global $DB, $security_config;
    
    $hour_ago = time() - 3600;
    $suspicious_indicators = [];
    
    // 检查短时间内大额充值
    if ($amount > 1000) {
        $recent_large = $DB->count("SELECT COUNT(*) FROM qingka_wangke_pay 
                                   WHERE uid = ? AND money > 1000 AND addtime > ?", 
                                   [$uid, date('Y-m-d H:i:s', $hour_ago)]);
        if ($recent_large > 2) {
            $suspicious_indicators[] = '短时间内多次大额充值';
        }
    }
    
    // 检查异常充值模式
    $recent_count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_pay 
                               WHERE uid = ? AND addtime > ?", 
                               [$uid, date('Y-m-d H:i:s', $hour_ago)]);
    
    if ($recent_count >= $security_config['suspicious_threshold']) {
        $suspicious_indicators[] = '短时间内频繁充值';
    }
    
    return $suspicious_indicators;
}

/**
 * 生成安全报告
 */
function generateSecurityReport() {
    global $DB;
    
    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    
    echo "=== 充值安全监控报告 ===\n";
    echo "生成时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 今日统计
    $today_events = $DB->query("SELECT action, risk_level, COUNT(*) as count 
                               FROM qingka_wangke_security_log 
                               WHERE DATE(created_at) = '{$today}' 
                               GROUP BY action, risk_level 
                               ORDER BY risk_level DESC, count DESC");
    
    echo "今日安全事件统计:\n";
    if ($today_events && $today_events->num_rows > 0) {
        while ($row = $today_events->fetch_assoc()) {
            echo "- {$row['action']} ({$row['risk_level']}): {$row['count']} 次\n";
        }
    } else {
        echo "- 暂无安全事件\n";
    }
    
    // 高风险事件
    $high_risk = $DB->query("SELECT ip, action, details, created_at 
                            FROM qingka_wangke_security_log 
                            WHERE risk_level = 'high' AND DATE(created_at) >= '{$yesterday}' 
                            ORDER BY created_at DESC LIMIT 10");
    
    echo "\n近期高风险事件:\n";
    if ($high_risk && $high_risk->num_rows > 0) {
        while ($row = $high_risk->fetch_assoc()) {
            echo "- {$row['created_at']} | {$row['ip']} | {$row['action']} | {$row['details']}\n";
        }
    } else {
        echo "- 暂无高风险事件\n";
    }
    
    // 频繁请求IP
    $frequent_ips = $DB->query("SELECT ip, COUNT(*) as count 
                               FROM qingka_wangke_security_log 
                               WHERE DATE(created_at) = '{$today}' 
                               GROUP BY ip 
                               HAVING count > 50 
                               ORDER BY count DESC LIMIT 10");
    
    echo "\n今日频繁请求IP:\n";
    if ($frequent_ips && $frequent_ips->num_rows > 0) {
        while ($row = $frequent_ips->fetch_assoc()) {
            echo "- {$row['ip']}: {$row['count']} 次请求\n";
        }
    } else {
        echo "- 暂无异常IP\n";
    }
    
    echo "\n=== 报告结束 ===\n";
}

// 如果直接运行此脚本，生成安全报告
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    createSecurityLogTable();
    cleanupOldLogs();
    generateSecurityReport();
}

?>
