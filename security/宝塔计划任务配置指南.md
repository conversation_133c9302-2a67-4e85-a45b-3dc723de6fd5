# 宝塔计划任务配置指南

## 📋 推荐的计划任务配置

### 1. 安全监控任务（推荐）

**任务名称：** 充值安全监控  
**执行周期：** 每15分钟  
**脚本类型：** Shell脚本  
**脚本内容：**
```bash
cd /www/wwwroot/************* && /usr/bin/php security/bt_cron_security.php
```

**说明：** 每15分钟检查一次安全威胁，及时发现异常行为

---

### 2. 详细报告任务（可选）

**任务名称：** 安全详细报告  
**执行周期：** 每天 08:00  
**脚本类型：** Shell脚本  
**脚本内容：**
```bash
cd /www/wwwroot/************* && /usr/bin/php security/recharge_security_monitor.php report
```

**说明：** 每天生成详细的安全报告

---

### 3. 日志清理任务（推荐）

**任务名称：** 安全日志清理  
**执行周期：** 每天 02:00  
**脚本类型：** Shell脚本  
**脚本内容：**
```bash
cd /www/wwwroot/************* && /usr/bin/php security/recharge_security_monitor.php cleanup
```

**说明：** 每天凌晨清理过期日志，节省存储空间

---

## 🔧 宝塔面板设置步骤

### 步骤1：进入计划任务
1. 登录宝塔面板
2. 点击左侧菜单 "计划任务"
3. 点击 "添加任务"

### 步骤2：配置基础信息
- **任务类型：** Shell脚本
- **任务名称：** 充值安全监控
- **执行周期：** 选择 "N分钟" → 输入 "15"

### 步骤3：设置脚本内容
```bash
cd /www/wwwroot/************* && /usr/bin/php security/bt_cron_security.php
```

### 步骤4：保存并启用
- 点击 "保存"
- 确保任务状态为 "启用"

---

## 📊 监控效果

### 实时监控内容
- ✅ IP请求频率异常检测
- ✅ 充值失败次数监控
- ✅ 可疑行为模式识别
- ✅ 系统安全状态评估

### 日志文件位置
- **安全日志：** `/www/wwwroot/*************/security/logs/security_YYYY-MM-DD.log`
- **数据库日志：** `qingka_wangke_security_log` 表

### 警报级别
- **INFO：** 正常运行信息
- **ALERT：** 安全警报（需要关注）
- **ERROR：** 系统错误（需要处理）

---

## 🚨 警报处理建议

### 高频IP警报
```
处理方式：
1. 检查IP来源和行为模式
2. 必要时添加IP黑名单
3. 调整频率限制参数
```

### 充值失败异常
```
处理方式：
1. 检查支付接口状态
2. 查看具体失败原因
3. 联系相关技术支持
```

### 可疑行为警报
```
处理方式：
1. 查看详细日志记录
2. 分析用户行为模式
3. 必要时暂停相关账户
```

---

## 📈 性能优化建议

### 1. 监控频率调整
- **高安全要求：** 每5-10分钟
- **一般要求：** 每15-30分钟
- **低频监控：** 每小时

### 2. 日志保留策略
- **默认：** 保留30天
- **高频环境：** 保留7-15天
- **低频环境：** 保留60-90天

### 3. 数据库优化
```sql
-- 定期优化安全日志表
OPTIMIZE TABLE qingka_wangke_security_log;

-- 检查索引使用情况
SHOW INDEX FROM qingka_wangke_security_log;
```

---

## 🔍 故障排查

### 常见问题

**1. 脚本执行失败**
```bash
# 检查PHP路径
which php

# 手动测试脚本
cd /www/wwwroot/*************
php security/bt_cron_security.php
```

**2. 权限问题**
```bash
# 设置正确权限
chmod 755 security/bt_cron_security.php
chown www:www security/
```

**3. 数据库连接问题**
- 检查 `confing/common.php` 配置
- 确认数据库服务正常运行

### 日志查看
```bash
# 查看最新日志
tail -f /www/wwwroot/*************/security/logs/security_$(date +%Y-%m-%d).log

# 查看宝塔计划任务日志
# 在宝塔面板 → 计划任务 → 查看日志
```

---

## ✅ 验证配置

### 手动测试
```bash
cd /www/wwwroot/*************
php security/bt_cron_security.php
```

### 预期输出
```
=== 充值安全监控 - 2024-XX-XX XX:XX:XX ===
✓ 安全检查正常
今日事件统计: 总计X次 [recharge_attempt(X), recharge_success(X)]
监控完成
```

配置完成后，系统将自动监控充值安全，及时发现和处理安全威胁！
