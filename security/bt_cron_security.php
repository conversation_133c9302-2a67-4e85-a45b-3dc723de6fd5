<?php
/**
 * 宝塔计划任务专用 - 充值安全监控脚本
 * 适合在宝塔面板中设置计划任务运行
 */

include(__DIR__ . '/../confing/common.php');

// 安全配置
$config = [
    'alert_threshold' => [
        'high_freq_ip' => 30,      // IP每小时请求超过30次报警
        'failed_recharge' => 10,    // 每小时充值失败超过10次报警
        'suspicious_count' => 5     // 每小时可疑行为超过5次报警
    ],
    'log_retention_days' => 30
];

/**
 * 创建安全表
 */
function initSecurityTables() {
    global $DB;
    
    // 安全日志表
    $sql1 = "CREATE TABLE IF NOT EXISTS `qingka_wangke_security_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `ip` varchar(45) NOT NULL,
        `uid` int(11) DEFAULT NULL,
        `action` varchar(50) NOT NULL,
        `details` text,
        `risk_level` enum('low','medium','high') DEFAULT 'low',
        `timestamp` int(11) NOT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_ip_action` (`ip`, `action`),
        KEY `idx_timestamp` (`timestamp`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    // 安全令牌表
    $sql2 = "CREATE TABLE IF NOT EXISTS `qingka_wangke_security_tokens` (
        `token` varchar(64) NOT NULL,
        `uid` int(11) NOT NULL,
        `data` text NOT NULL,
        `expire_time` int(11) NOT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`token`),
        KEY `idx_uid` (`uid`),
        KEY `idx_expire` (`expire_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $DB->query($sql1);
    $DB->query($sql2);
}

/**
 * 清理过期数据
 */
function cleanupExpiredData() {
    global $DB, $config;
    
    $expire_time = time() - ($config['log_retention_days'] * 24 * 3600);
    
    // 清理过期日志
    $sql1 = "DELETE FROM qingka_wangke_security_log WHERE timestamp < ?";
    $DB->prepare_query($sql1, [$expire_time]);
    
    // 清理过期令牌
    $sql2 = "DELETE FROM qingka_wangke_security_tokens WHERE expire_time < ?";
    $DB->prepare_query($sql2, [time()]);
    
    return true;
}

/**
 * 检查安全威胁
 */
function checkSecurityThreats() {
    global $DB, $config;
    
    $alerts = [];
    $hour_ago = time() - 3600;
    
    // 检查高频IP
    $sql = "SELECT ip, COUNT(*) as count FROM qingka_wangke_security_log 
            WHERE timestamp > ? GROUP BY ip HAVING count > ? ORDER BY count DESC LIMIT 5";
    $result = $DB->prepare_query($sql, [$hour_ago, $config['alert_threshold']['high_freq_ip']]);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $alerts[] = "高频IP: {$row['ip']} ({$row['count']}次/小时)";
        }
    }
    
    // 检查充值失败
    $sql = "SELECT COUNT(*) as count FROM qingka_wangke_security_log 
            WHERE action = 'recharge_failed' AND timestamp > ?";
    $result = $DB->prepare_getrow($sql, [$hour_ago]);
    
    if ($result['count'] > $config['alert_threshold']['failed_recharge']) {
        $alerts[] = "充值失败异常: {$result['count']}次/小时";
    }
    
    // 检查可疑行为
    $sql = "SELECT COUNT(*) as count FROM qingka_wangke_security_log 
            WHERE risk_level = 'high' AND timestamp > ?";
    $result = $DB->prepare_getrow($sql, [$hour_ago]);
    
    if ($result['count'] > $config['alert_threshold']['suspicious_count']) {
        $alerts[] = "可疑行为: {$result['count']}次/小时";
    }
    
    return $alerts;
}

/**
 * 生成统计报告
 */
function generateStats() {
    global $DB;
    
    $stats = [];
    
    // 今日统计
    $sql = "SELECT action, COUNT(*) as count FROM qingka_wangke_security_log 
            WHERE DATE(created_at) = CURDATE() GROUP BY action";
    $result = $DB->query($sql);
    
    $today_total = 0;
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $stats[$row['action']] = $row['count'];
            $today_total += $row['count'];
        }
    }
    
    $stats['total'] = $today_total;
    return $stats;
}

/**
 * 写入日志文件
 */
function writeLog($message, $type = 'info') {
    $log_dir = __DIR__ . '/logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/security_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}][{$type}] {$message}\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * 主执行函数
 */
function runSecurityCheck() {
    try {
        echo "=== 充值安全监控 - " . date('Y-m-d H:i:s') . " ===\n";
        
        // 1. 初始化表结构
        initSecurityTables();
        writeLog("安全表初始化完成");
        
        // 2. 清理过期数据
        cleanupExpiredData();
        writeLog("过期数据清理完成");
        
        // 3. 检查安全威胁
        $alerts = checkSecurityThreats();
        
        if (!empty($alerts)) {
            echo "⚠ 发现安全警报:\n";
            foreach ($alerts as $alert) {
                echo "  - {$alert}\n";
                writeLog($alert, 'ALERT');
            }
        } else {
            echo "✓ 安全检查正常\n";
            writeLog("安全检查正常，无异常行为");
        }
        
        // 4. 生成统计
        $stats = generateStats();
        $stats_msg = "今日事件统计: 总计{$stats['total']}次";
        if ($stats['total'] > 0) {
            unset($stats['total']);
            $details = [];
            foreach ($stats as $action => $count) {
                $details[] = "{$action}({$count})";
            }
            $stats_msg .= " [" . implode(", ", $details) . "]";
        }
        
        echo "{$stats_msg}\n";
        writeLog($stats_msg);
        
        echo "监控完成\n\n";
        
        return true;
        
    } catch (Exception $e) {
        $error = "监控执行错误: " . $e->getMessage();
        echo "❌ {$error}\n";
        writeLog($error, 'ERROR');
        return false;
    }
}

// 执行监控
runSecurityCheck();

?>
