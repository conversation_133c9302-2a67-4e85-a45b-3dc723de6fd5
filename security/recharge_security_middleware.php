<?php
/**
 * 充值安全中间件
 * 在充值处理前进行安全检查
 */

require_once('recharge_security_monitor.php');

class RechargeSecurityMiddleware {
    
    private $DB;
    private $clientip;
    
    public function __construct($database, $client_ip) {
        $this->DB = $database;
        $this->clientip = $client_ip;
        
        // 确保安全日志表存在
        createSecurityLogTable();
    }
    
    /**
     * 充值前安全检查
     */
    public function beforeRecharge($uid, $amount, $order_no = null) {
        $checks = [];
        
        // 1. IP频率检查
        $freq_check = checkRequestFrequency($this->clientip, 'recharge');
        if ($freq_check['blocked']) {
            logSecurityEvent($this->clientip, $uid, 'recharge_blocked', $freq_check['reason'], 'medium');
            return ['allowed' => false, 'reason' => $freq_check['reason']];
        }
        
        // 2. 可疑行为检查
        $suspicious = detectSuspiciousActivity($uid, $amount);
        if (!empty($suspicious)) {
            $details = '可疑行为: ' . implode(', ', $suspicious);
            logSecurityEvent($this->clientip, $uid, 'suspicious_recharge', $details, 'high');
            
            // 不阻止，但记录高风险日志
            $checks[] = '检测到可疑行为，已记录';
        }
        
        // 3. 订单重复检查
        if ($order_no && checkOrderDuplication($order_no)) {
            logSecurityEvent($this->clientip, $uid, 'duplicate_order', "重复处理订单: {$order_no}", 'high');
            return ['allowed' => false, 'reason' => '订单重复处理'];
        }
        
        // 4. 用户状态检查
        $user_check = $this->checkUserStatus($uid);
        if (!$user_check['valid']) {
            logSecurityEvent($this->clientip, $uid, 'invalid_user', $user_check['reason'], 'medium');
            return ['allowed' => false, 'reason' => $user_check['reason']];
        }
        
        // 记录正常请求
        logSecurityEvent($this->clientip, $uid, 'recharge_attempt', "金额: {$amount}", 'low');
        
        return ['allowed' => true, 'checks' => $checks];
    }
    
    /**
     * 充值后安全记录
     */
    public function afterRecharge($uid, $amount, $order_no, $success) {
        $action = $success ? 'recharge_success' : 'recharge_failed';
        $details = "订单: {$order_no}, 金额: {$amount}";
        $risk_level = $success ? 'low' : 'medium';
        
        logSecurityEvent($this->clientip, $uid, $action, $details, $risk_level);
        
        // 如果成功，记录订单处理
        if ($success && $order_no) {
            logSecurityEvent($this->clientip, $uid, 'recharge_process', "已处理订单: {$order_no}", 'low');
        }
    }
    
    /**
     * 检查用户状态
     */
    private function checkUserStatus($uid) {
        $sql = "SELECT active, money, addprice FROM qingka_wangke_user WHERE uid = ?";
        $user = $this->DB->prepare_getrow($sql, [$uid]);
        
        if (!$user) {
            return ['valid' => false, 'reason' => '用户不存在'];
        }
        
        if ($user['active'] != '1') {
            return ['valid' => false, 'reason' => '用户账户已禁用'];
        }
        
        if ($user['addprice'] < 0.1) {
            return ['valid' => false, 'reason' => '用户费率异常'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * 检查并发充值
     */
    public function checkConcurrentRecharge($uid) {
        // 检查是否有正在处理的充值
        $sql = "SELECT COUNT(*) FROM qingka_wangke_security_log 
                WHERE uid = ? AND action = 'recharge_attempt' 
                AND timestamp > ? AND timestamp NOT IN (
                    SELECT timestamp FROM qingka_wangke_security_log 
                    WHERE uid = ? AND action IN ('recharge_success', 'recharge_failed')
                )";
        $recent_time = time() - 30; // 30秒内
        $count = $this->DB->count($sql, [$uid, $recent_time, $uid]);
        
        return $count > 0;
    }
    
    /**
     * 生成安全令牌
     */
    public function generateSecurityToken($uid, $amount) {
        $data = [
            'uid' => $uid,
            'amount' => $amount,
            'timestamp' => time(),
            'ip' => $this->clientip
        ];
        
        $token = hash('sha256', json_encode($data) . 'security_salt_' . date('Y-m-d'));
        
        // 存储令牌（有效期5分钟）
        $expire_time = time() + 300;
        $sql = "INSERT INTO qingka_wangke_security_tokens (token, uid, data, expire_time) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE data = VALUES(data), expire_time = VALUES(expire_time)";
        $this->DB->prepare_query($sql, [$token, $uid, json_encode($data), $expire_time]);
        
        return $token;
    }
    
    /**
     * 验证安全令牌
     */
    public function validateSecurityToken($token, $uid) {
        $sql = "SELECT data, expire_time FROM qingka_wangke_security_tokens 
                WHERE token = ? AND uid = ? AND expire_time > ?";
        $result = $this->DB->prepare_getrow($sql, [$token, $uid, time()]);
        
        if (!$result) {
            return false;
        }
        
        $data = json_decode($result['data'], true);
        return $data['ip'] === $this->clientip;
    }
    
    /**
     * 清理过期令牌
     */
    public function cleanupExpiredTokens() {
        $sql = "DELETE FROM qingka_wangke_security_tokens WHERE expire_time < ?";
        $this->DB->prepare_query($sql, [time()]);
    }
}

/**
 * 创建安全令牌表
 */
function createSecurityTokenTable() {
    global $DB;
    
    $sql = "CREATE TABLE IF NOT EXISTS `qingka_wangke_security_tokens` (
        `token` varchar(64) NOT NULL,
        `uid` int(11) NOT NULL,
        `data` text NOT NULL,
        `expire_time` int(11) NOT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`token`),
        KEY `idx_uid` (`uid`),
        KEY `idx_expire` (`expire_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全令牌表'";
    
    $DB->query($sql);
}

// 初始化时创建必要的表
createSecurityTokenTable();

?>
