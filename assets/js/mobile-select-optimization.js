/**
 * 移动端下拉框优化脚本
 * 专门解决移动端下单页面项目选择下拉框名称显示不全的问题
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

(function() {
    'use strict';

    // 检测是否为移动设备
    function isMobileDevice() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 初始化移动端下拉框优化
    function initMobileSelectOptimization() {
        // 添加优化类名到body
        if (isMobileDevice()) {
            document.body.classList.add('mobile-select-optimization');
        }

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (isMobileDevice()) {
                document.body.classList.add('mobile-select-optimization');
            } else {
                document.body.classList.remove('mobile-select-optimization');
            }
        });
    }

    // 优化下拉框选项显示
    function optimizeSelectOptions() {
        // 等待Vue和Element UI加载完成
        if (typeof Vue === 'undefined' || typeof ELEMENT === 'undefined') {
            setTimeout(optimizeSelectOptions, 100);
            return;
        }

        // 监听下拉框打开事件
        document.addEventListener('click', function(e) {
            const selectElement = e.target.closest('.el-select');
            if (selectElement && isMobileDevice()) {
                setTimeout(function() {
                    optimizeDropdownOptions();
                }, 50);
            }
        });
    }

    // 优化下拉框选项内容
    function optimizeDropdownOptions() {
        const dropdowns = document.querySelectorAll('.el-select-dropdown:not(.el-select-dropdown--hidden)');
        
        dropdowns.forEach(function(dropdown) {
            const options = dropdown.querySelectorAll('.el-option');
            
            options.forEach(function(option) {
                // 检查是否已经优化过
                if (option.classList.contains('mobile-optimized')) {
                    return;
                }

                // 添加优化标记
                option.classList.add('mobile-optimized');

                // 获取选项文本
                const optionText = option.textContent || option.innerText;
                
                // 如果文本过长，添加特殊类名
                if (optionText.length > 30) {
                    option.classList.add('long-text-option');
                }

                // 优化选项内容结构
                optimizeOptionContent(option);
            });

            // 调整下拉框位置
            adjustDropdownPosition(dropdown);
        });
    }

    // 优化选项内容结构
    function optimizeOptionContent(option) {
        const originalContent = option.innerHTML;
        
        // 检查是否包含项目名称和价格的结构
        if (originalContent.includes('project-name') && originalContent.includes('project-price')) {
            return; // 已经是优化后的结构
        }

        // 尝试解析原始内容
        const textContent = option.textContent || option.innerText;
        
        // 检查是否包含价格信息（积分）
        const priceMatch = textContent.match(/(\d+(?:\.\d+)?)\s*积分/);
        
        if (priceMatch) {
            const price = priceMatch[0];
            const projectName = textContent.replace(price, '').replace(/→/g, '').trim();
            
            // 重构HTML结构
            option.innerHTML = `
                <div class="el-option-content">
                    <span class="project-name">${projectName}</span>
                    <span class="project-price">${price}</span>
                </div>
            `;
        }
    }

    // 调整下拉框位置
    function adjustDropdownPosition(dropdown) {
        if (!isMobileDevice()) return;

        const rect = dropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 确保下拉框不超出屏幕右边界
        if (rect.right > viewportWidth) {
            const offset = rect.right - viewportWidth + 10;
            dropdown.style.left = (parseInt(dropdown.style.left) - offset) + 'px';
        }

        // 确保下拉框不超出屏幕左边界
        if (rect.left < 0) {
            dropdown.style.left = '10px';
        }

        // 如果下拉框太高，限制高度并启用滚动
        if (rect.height > viewportHeight * 0.6) {
            dropdown.style.maxHeight = (viewportHeight * 0.6) + 'px';
            dropdown.style.overflowY = 'auto';
        }
    }

    // 添加触摸优化
    function addTouchOptimization() {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 优化触摸滚动
        document.addEventListener('touchstart', function(e) {
            const dropdown = e.target.closest('.el-select-dropdown');
            if (dropdown) {
                dropdown.style.webkitOverflowScrolling = 'touch';
            }
        });
    }

    // 监听Element UI组件更新
    function watchElementUIUpdates() {
        // 使用MutationObserver监听DOM变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('el-select-dropdown')) {
                            setTimeout(function() {
                                optimizeDropdownOptions();
                            }, 10);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加自定义样式
    function addCustomStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('mobile-select-optimization-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'mobile-select-optimization-styles';
        style.textContent = `
            /* 移动端下拉框快速修复样式 */
            @media only screen and (max-width: 768px) {
                .mobile-select-optimization .el-select-dropdown .el-option {
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    height: auto !important;
                    min-height: 50px !important;
                    padding: 15px 16px !important;
                    line-height: 1.4 !important;
                }
                
                .mobile-select-optimization .el-option .project-name {
                    display: block !important;
                    width: 100% !important;
                    margin-bottom: 5px !important;
                    font-size: 15px !important;
                    word-break: break-all !important;
                }
                
                .mobile-select-optimization .el-option .project-price {
                    display: inline-block !important;
                    font-size: 13px !important;
                    color: #409eff !important;
                    background: #ecf5ff !important;
                    padding: 3px 8px !important;
                    border-radius: 10px !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 初始化函数
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 初始化各项优化
        initMobileSelectOptimization();
        addCustomStyles();
        optimizeSelectOptions();
        addTouchOptimization();
        watchElementUIUpdates();

        console.log('移动端下拉框优化已启用');
    }

    // 启动初始化
    init();

    // 暴露全局方法供外部调用
    window.MobileSelectOptimization = {
        init: init,
        optimizeDropdownOptions: optimizeDropdownOptions,
        isMobileDevice: isMobileDevice
    };

})();
