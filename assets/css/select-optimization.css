/**
 * 下拉选择框优化样式
 * 解决商品选择下拉框显示格式和长文本滑动问题
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

/* ========== 基础下拉框样式优化 ========== */
.el-select-dropdown {
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e4e7ed !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.el-select-dropdown .el-option {
    padding: 12px 16px !important;
    min-height: 60px !important;
    border-bottom: 1px solid #f5f5f5 !important;
    transition: all 0.2s ease !important;
}

.el-select-dropdown .el-option:last-child {
    border-bottom: none !important;
}

.el-select-dropdown .el-option:hover {
    background-color: #f8f9fa !important;
}

.el-select-dropdown .el-option.selected {
    background-color: #ecf5ff !important;
    color: #409eff !important;
}

/* ========== 选项内容布局 ========== */
.el-option .el-option-content {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    width: 100% !important;
    gap: 12px !important;
    overflow: hidden !important;
}

/* 价格标签样式 - 现在在前面 */
.el-option .project-price {
    flex-shrink: 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    text-align: center !important;
    min-width: 70px !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    order: 1 !important;
}

/* 项目名称样式 - 现在在后面，支持手动滑动 */
.el-option .project-name {
    flex: 1 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #333 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    order: 2 !important;

    /* 强制显示水平滚动条 */
    white-space: nowrap !important;
    overflow-x: scroll !important;
    overflow-y: hidden !important;
    max-width: 250px !important;
    min-height: 24px !important;
    padding: 2px 0 8px 0 !important;
    position: relative !important;

    /* Firefox 滚动条 */
    scrollbar-width: thin !important;
    scrollbar-color: #bbb #f0f0f0 !important;
}

/* Webkit浏览器滚动条样式 - 加大尺寸便于操作 */
.el-option .project-name::-webkit-scrollbar {
    height: 12px !important;
    width: 12px !important;
    background: #f8f9fa !important;
}

.el-option .project-name::-webkit-scrollbar-track {
    background: #e9ecef !important;
    border-radius: 6px !important;
    border: 1px solid #dee2e6 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb {
    background: #6c757d !important;
    border-radius: 6px !important;
    border: 2px solid #e9ecef !important;
    min-width: 30px !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:hover {
    background: #495057 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:active {
    background: #343a40 !important;
}

/* 确保滚动条角落样式 */
.el-option .project-name::-webkit-scrollbar-corner {
    background: #f8f9fa !important;
}

/* 特别长的文本处理 */
.el-option .project-name.long-text {
    font-size: 13px !important;
}

/* 滑动提示样式 */
.el-option .project-name[title*="可以左右滑动"] {
    position: relative !important;
}

.el-option .project-name[title*="可以左右滑动"]:before {
    content: "↔" !important;
    position: absolute !important;
    right: -2px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 10px !important;
    color: #999 !important;
    opacity: 0.7 !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

/* 滑动时的视觉反馈 */
.el-option .project-name:active {
    background-color: rgba(64, 158, 255, 0.05) !important;
    border-radius: 3px !important;
}

/* ========== 移动端适配 ========== */
@media only screen and (max-width: 768px) {
    .el-select-dropdown {
        min-width: 300px !important;
        max-width: 95vw !important;
        left: 2.5vw !important;
        right: 2.5vw !important;
        margin: 0 !important;
    }

    .el-select-dropdown .el-option {
        min-height: 70px !important;
        padding: 16px !important;
    }

    .el-option .el-option-content {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .el-option .project-price {
        order: 1 !important;
        align-self: flex-end !important;
        font-size: 12px !important;
        padding: 3px 10px !important;
        min-width: 60px !important;
    }

    .el-option .project-name {
        order: 2 !important;
        width: 100% !important;
        font-size: 15px !important;

        /* 移动端也支持手动滑动 */
        white-space: nowrap !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        padding: 4px 0 !important;

        /* 移动端滚动条始终显示 */
        scrollbar-width: thin !important;
    }

    /* 移动端滚动条样式 - 更大更明显 */
    .el-option .project-name::-webkit-scrollbar {
        height: 10px !important;
        background: #f8f9fa !important;
    }

    .el-option .project-name::-webkit-scrollbar-track {
        background: #e9ecef !important;
        border-radius: 5px !important;
        border: 1px solid #dee2e6 !important;
    }

    .el-option .project-name::-webkit-scrollbar-thumb {
        background: #6c757d !important;
        border-radius: 5px !important;
        border: 1px solid #e9ecef !important;
        min-width: 40px !important;
    }

    .el-option .project-name::-webkit-scrollbar-thumb:active {
        background: #495057 !important;
    }
}

/* ========== 超小屏幕适配 ========== */
@media only screen and (max-width: 375px) {
    .el-select-dropdown {
        min-width: 280px !important;
        max-width: 98vw !important;
        left: 1vw !important;
        right: 1vw !important;
    }

    .el-select-dropdown .el-option {
        min-height: 65px !important;
        padding: 14px !important;
    }

    .el-option .project-name {
        font-size: 14px !important;
    }

    .el-option .project-price {
        font-size: 11px !important;
        padding: 2px 8px !important;
        min-width: 55px !important;
    }

    /* 超小屏幕滚动条优化 */
    .el-option .project-name::-webkit-scrollbar {
        height: 5px !important;
    }

    .el-option .project-name::-webkit-scrollbar-thumb {
        background: #999 !important;
    }
}

/* ========== 选择框输入框优化 ========== */
.el-select .el-input__inner {
    font-size: 14px !important;
    height: 40px !important;
    line-height: 40px !important;
    border-radius: 6px !important;
    border: 1px solid #dcdfe6 !important;
    transition: all 0.2s ease !important;
}

.el-select .el-input__inner:focus {
    border-color: #409eff !important;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* ========== 下拉箭头优化 ========== */
.el-select .el-input__suffix {
    right: 8px !important;
}

.el-select .el-input__suffix .el-input__suffix-inner {
    color: #c0c4cc !important;
}

/* ========== 搜索高亮 ========== */
.el-select-dropdown .el-option .highlight {
    background-color: #fff2cc !important;
    color: #e6a23c !important;
    font-weight: 600 !important;
}

/* ========== 无数据提示 ========== */
.el-select-dropdown .el-select-dropdown__empty {
    padding: 20px !important;
    text-align: center !important;
    color: #999 !important;
    font-size: 14px !important;
}

/* ========== 加载状态 ========== */
.el-select-dropdown .el-select-dropdown__loading {
    padding: 20px !important;
    text-align: center !important;
    color: #999 !important;
    font-size: 14px !important;
}

/* ========== 滚动条优化 ========== */
.el-select-dropdown::-webkit-scrollbar {
    width: 6px !important;
}

.el-select-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.el-select-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.el-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}
