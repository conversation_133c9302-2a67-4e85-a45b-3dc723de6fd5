/**
 * 简化滚动条样式
 * 只添加滚动条功能，不影响现有布局
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

/* ========== 仅添加滚动条功能 ========== */
.el-option .project-name {
    /* 水平滚动 */
    overflow-x: auto !important;
    overflow-y: hidden !important;
    
    /* 确保有足够空间显示滚动条 */
    padding-bottom: 8px !important;
    
    /* Firefox 滚动条 */
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
}

/* ========== Webkit 滚动条样式 ========== */
.el-option .project-name::-webkit-scrollbar {
    height: 8px !important;
    background-color: #f1f1f1 !important;
}

.el-option .project-name::-webkit-scrollbar-track {
    background-color: #f1f1f1 !important;
    border-radius: 4px !important;
}

.el-option .project-name::-webkit-scrollbar-thumb {
    background-color: #888 !important;
    border-radius: 4px !important;
    border: 1px solid #f1f1f1 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:hover {
    background-color: #555 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:active {
    background-color: #333 !important;
}

/* ========== 移动端优化 ========== */
@media only screen and (max-width: 768px) {
    .el-option .project-name {
        padding-bottom: 10px !important;
    }
    
    .el-option .project-name::-webkit-scrollbar {
        height: 10px !important;
    }
}

/* ========== 确保在下拉框中生效 ========== */
.el-select-dropdown .el-option .project-name {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    padding-bottom: 8px !important;
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
}
