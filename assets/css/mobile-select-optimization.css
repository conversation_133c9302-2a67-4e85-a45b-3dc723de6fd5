/**
 * 移动端下拉框项目名称显示优化
 * 专门解决移动端下单页面项目选择下拉框名称显示不全的问题
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

/* ========== 基础下拉框优化 ========== */
.mobile-select-optimization .el-select-dropdown {
    /* 确保下拉框宽度适配 */
    min-width: 320px !important;
    max-width: 95vw !important;
    /* 确保在最上层显示 */
    z-index: 9999 !important;
    /* 添加阴影效果 */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border-radius: 8px !important;
    border: 1px solid #e4e7ed !important;
}

/* ========== 选项样式优化 ========== */
.mobile-select-optimization .el-select-dropdown .el-option {
    /* 自适应高度 */
    height: auto !important;
    min-height: 60px !important;
    /* 增加内边距提升触摸体验 */
    padding: 16px 20px !important;
    /* 文本换行设置 */
    white-space: normal !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow: visible !important;
    /* 行高设置 */
    line-height: 1.5 !important;
    /* 添加分隔线 */
    border-bottom: 1px solid #f5f5f5 !important;
    /* 字体设置 */
    font-size: 15px !important;
    color: #333 !important;
}

.mobile-select-optimization .el-select-dropdown .el-option:last-child {
    border-bottom: none !important;
}

.mobile-select-optimization .el-select-dropdown .el-option:hover {
    background-color: #f8f9fa !important;
}

.mobile-select-optimization .el-select-dropdown .el-option.selected {
    background-color: #ecf5ff !important;
    color: #409eff !important;
}

/* ========== 选项内容布局优化 ========== */
.mobile-select-optimization .el-option .el-option-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100% !important;
    gap: 8px !important;
}

/* 项目名称样式 */
.mobile-select-optimization .el-option .project-name {
    width: 100% !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #333 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    /* 确保长文本能够完整显示 */
    white-space: normal !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
}

/* 价格标签样式 */
.mobile-select-optimization .el-option .project-price {
    align-self: flex-end !important;
    font-size: 13px !important;
    color: #409eff !important;
    background: #ecf5ff !important;
    padding: 4px 12px !important;
    border-radius: 16px !important;
    white-space: nowrap !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

/* ========== 输入框优化 ========== */
.mobile-select-optimization .el-select .el-input__inner {
    /* 防止iOS自动缩放 */
    font-size: 16px !important;
    /* 增加高度提升触摸体验 */
    height: 48px !important;
    line-height: 48px !important;
    /* 文本溢出处理 */
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    /* 内边距调整 */
    padding: 0 40px 0 15px !important;
}

/* 下拉箭头样式 */
.mobile-select-optimization .el-select .el-input .el-select__caret {
    color: #c0c4cc !important;
    font-size: 14px !important;
}

/* ========== 移动端专用样式 ========== */
@media only screen and (max-width: 768px) {
    /* 下拉框容器 */
    .mobile-select-optimization .el-select-dropdown {
        min-width: 300px !important;
        max-width: 96vw !important;
        left: 2vw !important;
        right: 2vw !important;
        margin: 0 !important;
    }

    /* 选项样式 */
    .mobile-select-optimization .el-select-dropdown .el-option {
        min-height: 70px !important;
        padding: 18px 16px !important;
        font-size: 16px !important;
    }

    /* 项目名称 */
    .mobile-select-optimization .el-option .project-name {
        font-size: 16px !important;
        line-height: 1.3 !important;
    }

    /* 价格标签 */
    .mobile-select-optimization .el-option .project-price {
        font-size: 14px !important;
        padding: 6px 14px !important;
    }
}

/* ========== 超小屏幕适配 ========== */
@media only screen and (max-width: 375px) {
    .mobile-select-optimization .el-select-dropdown {
        min-width: 280px !important;
        max-width: 98vw !important;
        left: 1vw !important;
        right: 1vw !important;
    }

    .mobile-select-optimization .el-select-dropdown .el-option {
        min-height: 65px !important;
        padding: 16px 14px !important;
    }

    .mobile-select-optimization .el-option .project-name {
        font-size: 15px !important;
    }

    .mobile-select-optimization .el-option .project-price {
        font-size: 13px !important;
        padding: 5px 12px !important;
    }
}

/* ========== 长文本特殊处理 ========== */
.mobile-select-optimization .el-option .project-name.long-text {
    /* 对于特别长的文本，使用更小的字体 */
    font-size: 14px !important;
    line-height: 1.3 !important;
}

/* ========== 搜索框优化 ========== */
.mobile-select-optimization .el-select-dropdown .el-select-dropdown__wrap {
    max-height: 60vh !important; /* 限制最大高度 */
}

.mobile-select-optimization .el-select-dropdown .el-scrollbar__view {
    padding: 0 !important;
}

/* ========== 无结果提示优化 ========== */
.mobile-select-optimization .el-select-dropdown .el-select-dropdown__empty {
    padding: 20px !important;
    text-align: center !important;
    color: #999 !important;
    font-size: 14px !important;
}

/* ========== 加载状态优化 ========== */
.mobile-select-optimization .el-select-dropdown .el-select-dropdown__loading {
    padding: 20px !important;
    text-align: center !important;
    color: #999 !important;
}

/* ========== 响应式字体大小 ========== */
@media only screen and (max-width: 320px) {
    .mobile-select-optimization .el-option .project-name {
        font-size: 14px !important;
    }
    
    .mobile-select-optimization .el-option .project-price {
        font-size: 12px !important;
    }
}

/* ========== 高分辨率屏幕优化 ========== */
@media only screen and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-select-optimization .el-select-dropdown {
        border-width: 0.5px !important;
    }
}

/* ========== 深色模式支持 ========== */
@media (prefers-color-scheme: dark) {
    .mobile-select-optimization .el-select-dropdown {
        background-color: #2d2d2d !important;
        border-color: #404040 !important;
    }

    .mobile-select-optimization .el-select-dropdown .el-option {
        color: #e0e0e0 !important;
        border-bottom-color: #404040 !important;
    }

    .mobile-select-optimization .el-option .project-name {
        color: #e0e0e0 !important;
    }

    .mobile-select-optimization .el-select-dropdown .el-option:hover {
        background-color: #404040 !important;
    }
}

/* ========== 辅助功能优化 ========== */
.mobile-select-optimization .el-select-dropdown .el-option:focus {
    outline: 2px solid #409eff !important;
    outline-offset: -2px !important;
}

/* ========== 动画效果 ========== */
.mobile-select-optimization .el-select-dropdown {
    transition: all 0.3s ease !important;
}

.mobile-select-optimization .el-select-dropdown .el-option {
    transition: background-color 0.2s ease !important;
}
