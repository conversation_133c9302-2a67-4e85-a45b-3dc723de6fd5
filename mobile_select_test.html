<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端下拉框优化测试</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css">
    <!-- 移动端下拉框优化CSS -->
    <link rel="stylesheet" href="assets/css/mobile-select-optimization.css" type="text/css" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .test-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .device-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 10px;
            margin-top: 20px;
            font-size: 12px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1 class="test-title">移动端下拉框优化测试</h1>
        
        <div class="test-info">
            <strong>测试说明：</strong><br>
            1. 在移动设备上打开此页面<br>
            2. 点击下方的项目选择下拉框<br>
            3. 观察长项目名称是否能完整显示<br>
            4. 检查价格标签是否正确显示
        </div>
        
        <div class="form-group">
            <label class="form-label">选择项目（优化前对比）：</label>
            <el-select 
                v-model="selectedProject1" 
                placeholder="请选择项目" 
                style="width: 100%;"
                class="normal-select"
            >
                <el-option 
                    v-for="project in testProjects" 
                    :key="project.id" 
                    :label="project.name + ' → ' + project.price + '积分'" 
                    :value="project.id"
                >
                </el-option>
            </el-select>
        </div>
        
        <div class="form-group">
            <label class="form-label">选择项目（优化后效果）：</label>
            <el-select 
                v-model="selectedProject2" 
                placeholder="请选择项目" 
                style="width: 100%;"
                class="optimized-select"
            >
                <el-option 
                    v-for="project in testProjects" 
                    :key="project.id" 
                    :label="project.name" 
                    :value="project.id"
                >
                    <div class="el-option-content">
                        <span class="project-name">{{ project.name }}</span>
                        <span class="project-price">{{ project.price }}积分</span>
                    </div>
                </el-option>
            </el-select>
        </div>
        
        <div class="form-group">
            <label class="form-label">可搜索的项目选择：</label>
            <el-select 
                v-model="selectedProject3" 
                placeholder="输入关键词搜索项目" 
                style="width: 100%;"
                filterable
                class="optimized-select"
            >
                <el-option 
                    v-for="project in testProjects" 
                    :key="project.id" 
                    :label="project.name" 
                    :value="project.id"
                >
                    <div class="el-option-content">
                        <span class="project-name">{{ project.name }}</span>
                        <span class="project-price">{{ project.price }}积分</span>
                    </div>
                </el-option>
            </el-select>
        </div>
        
        <div class="device-info">
            <strong>设备信息：</strong><br>
            屏幕宽度: {{ screenWidth }}px<br>
            用户代理: {{ userAgent }}<br>
            是否移动设备: {{ isMobile ? '是' : '否' }}
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <!-- Element UI JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
    <!-- 移动端下拉框优化JavaScript -->
    <script src="assets/js/mobile-select-optimization.js"></script>
    
    <script>
        new Vue({
            el: '#app',
            data: {
                selectedProject1: '',
                selectedProject2: '',
                selectedProject3: '',
                screenWidth: window.innerWidth,
                userAgent: navigator.userAgent,
                isMobile: window.innerWidth <= 768,
                testProjects: [
                    {
                        id: 1,
                        name: '智慧树网课代刷服务平台',
                        price: 15.5
                    },
                    {
                        id: 2,
                        name: '超星学习通网课代刷代看代做作业考试服务',
                        price: 20.0
                    },
                    {
                        id: 3,
                        name: '中国大学MOOC慕课网课代刷代看代做作业考试服务平台',
                        price: 25.5
                    },
                    {
                        id: 4,
                        name: '学堂在线网课代刷代看代做作业考试服务',
                        price: 18.0
                    },
                    {
                        id: 5,
                        name: '国家智慧教育平台网课代刷代看代做作业考试服务系统',
                        price: 30.0
                    },
                    {
                        id: 6,
                        name: '腾讯课堂网课代刷代看代做作业考试服务平台系统',
                        price: 22.5
                    },
                    {
                        id: 7,
                        name: '网易云课堂网课代刷代看代做作业考试服务',
                        price: 19.0
                    },
                    {
                        id: 8,
                        name: '知到智慧树网课代刷代看代做作业考试服务平台',
                        price: 16.5
                    },
                    {
                        id: 9,
                        name: '雨课堂网课代刷代看代做作业考试服务系统平台',
                        price: 21.0
                    },
                    {
                        id: 10,
                        name: '职教云网课代刷代看代做作业考试服务平台系统',
                        price: 17.5
                    }
                ]
            },
            mounted() {
                // 监听窗口大小变化
                window.addEventListener('resize', () => {
                    this.screenWidth = window.innerWidth;
                    this.isMobile = window.innerWidth <= 768;
                });
                
                console.log('移动端下拉框优化测试页面已加载');
            }
        });
    </script>
</body>
</html>
