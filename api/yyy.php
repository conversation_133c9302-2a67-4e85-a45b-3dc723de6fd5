<?php

// 引入公共配置文件
include('../confing/common.php');

// 获取并处理 GET 参数
$pricee = trim(strip_tags(daddslashes($_GET['pricee'])));

// 查询 qingka_wangke_huoyuan 表获取相关信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE  instr(pt,'yyy') or instr(name,'yyy')");
$hid = $a["hid"];

// 查询 qingka_wangke_fenlei 表获取分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'yyy') ORDER BY id DESC LIMIT 0, 1");
$category = $b["id"];

// 准备请求数据
$data = array(
    "uid" => $a["user"],
    "key" => $a["pass"]
);

// 构建请求 URL
$er_url = "{$a["url"]}/api/site";

// 发送请求并获取结果
$result = get_url($er_url, $data);
$result = json_decode($result, true);

// 检查对接结果
if ($result["code"] != "200") {
    jsonReturn(1, $result["message"]);
}

// 查询 qingka_wangke_class 表的最大 sort 值
$max_sort_query = "SELECT MAX(sort) as max_sort FROM qingka_wangke_class";
$max_sort_result = $DB->query($max_sort_query);
$max_sort_row = $DB->fetch($max_sort_result);
$current_sort = $max_sort_row['max_sort'] ?? 0;

// 获取当前时间
$now_time = date('Y-m-d H:i:s');

// 初始化插入和更新记录的计数器
$inserted_count = 0;
$updated_count = 0;

// 初始化输出数组
$output = [];
$ids = [];

// 遍历结果数据列表
foreach ($result["data"]["list"] as $data_item) {
    $id = $data_item['id'];
    $ids[] = $id;

    // 清理项目名称，移除常见的yyy教育前缀
    $original_name = $data_item['name'];
    $cleaned_name = $original_name;

    // 定义需要移除的前缀
    $prefixes_to_remove = array(
        'YYY继教-'
    );

    // 移除前缀
    foreach ($prefixes_to_remove as $prefix) {
        if (strpos($cleaned_name, $prefix) === 0) {
            $cleaned_name = substr($cleaned_name, strlen($prefix));
            break;
        }
    }

    $name = $DB->escape($cleaned_name);
    $price = $data_item['price'] * $pricee;
    $content = $DB->escape($data_item['trans']);

    // 查询 qingka_wangke_class 表是否存在对应记录
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$id' LIMIT 1");

    if (!$rs) {
        // 若记录不存在，执行插入操作
        $sql = "INSERT INTO qingka_wangke_class
                (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, addtime, status, fenlei)
                VALUES
                ('$current_sort', '$name', '$id', '$id', '$price', '$hid', '$hid', '*', '$content', '$now_time', '1', '$category')";

        $is = $DB->query($sql);
        if ($is) {
            $inserted_count++;
            $current_sort++;
        }
    } else {
        // 若记录存在，执行更新操作
        $sql = "UPDATE qingka_wangke_class
                SET name = '$name',
                    price = '$price',
                    content = '$content'
                WHERE docking = '$hid' AND noun = '$id'";

        $is = $DB->query($sql);
        if ($is) {
            $updated_count++;
        }
    }
}

//下架已下架的项目
if (!empty($ids)) {
    $ids_str = implode(',', array_map(function($id) use ($DB) {
        return "'" . $DB->escape($id) . "'";
    }, $ids));
    $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE docking = '$hid' AND noun NOT IN ($ids_str)";
    $DB->query($sql);
}

// 返回操作结果
echo ("插入操作完成。成功上架 {$inserted_count} 条记录，更新 {$updated_count} 条记录。\n\n");
?>
