<?php
/**
 * 8090教育智能商品同步脚本
 * 优化版：智能检测API限制，优先一次性获取，必要时自动分页
 * 
 * 特性：
 * 1. 优先尝试一次性获取全部数据
 * 2. 自动检测API限制并调整策略
 * 3. 智能错误处理和重试机制
 * 4. 详细的进度显示和日志记录
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 获取并处理 GET 参数
$pricee = isset($_GET['pricee']) ? floatval($_GET['pricee']) : 3;

// 查询 qingka_wangke_huoyuan 表获取相关信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE instr(pt,'8090edu') or instr(name,'8090edu')");
if (!$a) {
    echo "[ERROR] 未找到8090教育货源配置\n";
    jsonReturn(1, "未找到8090教育货源配置");
}
$hid = $a["hid"];

// 查询 qingka_wangke_fenlei 表获取分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'8090edu') ORDER BY id DESC LIMIT 0, 1");
$category = $b["id"];

echo "[START] 8090教育智能商品同步开始\n";
echo "[INFO] 同步时间: " . date('Y-m-d H:i:s') . "\n";
echo "[INFO] 价格倍数: {$pricee}\n";
echo "[INFO] 分类ID: {$category}\n";
echo "=" . str_repeat("=", 50) . "\n";

/**
 * 获取认证Token
 */
function getAuthToken($a) {
    echo "[AUTH] 正在获取认证Token...\n";
    
    $login_data = array(
        "username" => $a["user"],
        "password" => $a["pass"]
    );

    $login_url = "{$a["url"]}/api/auth/login";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    $login_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($curl_error) {
        echo "[ERROR] 登录网络错误: {$curl_error}\n";
        jsonReturn(1, "登录网络错误: {$curl_error}");
    }

    $login_result = json_decode($login_result, true);

    if (!$login_result || $login_result["code"] != 200) {
        $error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
        echo "[ERROR] 登录失败: {$error_msg}\n";
        jsonReturn(1, $error_msg);
    }

    $token = $login_result["data"]["token"];
    echo "[SUCCESS] Token获取成功\n";
    return $token;
}

/**
 * 智能获取所有网站数据
 */
function getAllSitesIntelligent($a, $token) {
    echo "\n📡 开始智能获取网站列表...\n";
    
    // 策略1: 尝试一次性获取大量数据
    $strategies = [
        ['pageSize' => 50000, 'desc' => '超大批量获取'],
        ['pageSize' => 10000, 'desc' => '大批量获取'],
        ['pageSize' => 5000, 'desc' => '中批量获取'],
        ['pageSize' => 1000, 'desc' => '标准批量获取']
    ];
    
    foreach ($strategies as $strategy) {
        echo "🔄 尝试{$strategy['desc']} (pageSize: {$strategy['pageSize']})...\n";
        
        $result = fetchSitesWithPageSize($a, $token, $strategy['pageSize']);
        
        if ($result['success']) {
            echo "✅ {$strategy['desc']}成功！\n";
            return $result;
        } else {
            echo "⚠️  {$strategy['desc']}失败: {$result['error']}\n";
        }
    }
    
    // 如果所有策略都失败，使用传统分页方式
    echo "🔄 回退到传统分页获取方式...\n";
    return fetchSitesWithPagination($a, $token);
}

/**
 * 使用指定pageSize获取数据
 */
function fetchSitesWithPageSize($a, $token, $pageSize) {
    $sites_url = "{$a["url"]}/api/price/sites?page=1&pageSize={$pageSize}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $sites_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 增加超时时间
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $sites_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($curl_error) {
        return ['success' => false, 'error' => "网络错误: {$curl_error}"];
    }
    
    if ($http_code !== 200) {
        return ['success' => false, 'error' => "HTTP错误: {$http_code}"];
    }
    
    $sites_result = json_decode($sites_result, true);
    
    if (!$sites_result || $sites_result["code"] != 200) {
        $error_msg = isset($sites_result["message"]) ? $sites_result["message"] : "API响应异常";
        return ['success' => false, 'error' => $error_msg];
    }
    
    if (!isset($sites_result["data"]["list"]) || !is_array($sites_result["data"]["list"])) {
        return ['success' => false, 'error' => "数据格式异常"];
    }
    
    $all_sites = $sites_result["data"]["list"];
    $total_records = isset($sites_result["data"]["total"]) ? $sites_result["data"]["total"] : count($all_sites);
    
    // 检查是否获取完整
    if (count($all_sites) < $total_records && count($all_sites) >= $pageSize) {
        return ['success' => false, 'error' => "数据可能被截断"];
    }
    
    return [
        'success' => true,
        'data' => $all_sites,
        'total' => $total_records,
        'method' => 'single_request'
    ];
}

/**
 * 传统分页获取方式（兜底方案）
 */
function fetchSitesWithPagination($a, $token) {
    $all_sites = [];
    $page = 1;
    $pageSize = 500; // 使用较大的分页大小
    $total_pages = 1;
    
    do {
        echo "📄 获取第 {$page} 页 (pageSize: {$pageSize})...\n";
        
        $sites_url = "{$a["url"]}/api/price/sites?page={$page}&pageSize={$pageSize}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $sites_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $sites_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            return ['success' => false, 'error' => "分页获取网络错误: {$curl_error}"];
        }
        
        $sites_result = json_decode($sites_result, true);
        
        if (!$sites_result || $sites_result["code"] != 200) {
            $error_msg = isset($sites_result["message"]) ? $sites_result["message"] : "分页获取失败";
            return ['success' => false, 'error' => $error_msg];
        }
        
        if (isset($sites_result["data"]["list"])) {
            $all_sites = array_merge($all_sites, $sites_result["data"]["list"]);
            
            if ($page == 1 && isset($sites_result["data"]["total"])) {
                $total_records = $sites_result["data"]["total"];
                $total_pages = ceil($total_records / $pageSize);
                echo "📊 总记录数: {$total_records}，总页数: {$total_pages}\n";
            }
            
            echo "✅ 第 {$page} 页获取成功，累计: " . count($all_sites) . " 个网站\n";
        }
        
        $page++;
        
        // 添加延迟避免请求过快
        if ($page <= $total_pages) {
            usleep(100000); // 0.1秒延迟
        }
        
    } while ($page <= $total_pages);
    
    return [
        'success' => true,
        'data' => $all_sites,
        'total' => count($all_sites),
        'method' => 'pagination'
    ];
}

/**
 * 处理网站数据
 */
function processSitesData($all_sites, $a, $pricee, $category) {
    global $DB;
    
    $hid = $a["hid"];
    $total_sites = count($all_sites);
    
    echo "\n🔄 开始处理 {$total_sites} 个网站数据...\n";
    
    // 设置脚本执行时间限制
    set_time_limit(0);
    
    // 查询最大sort值
    $max_sort_query = "SELECT MAX(sort) as max_sort FROM qingka_wangke_class";
    $max_sort_result = $DB->query($max_sort_query);
    $max_sort_row = $max_sort_result->fetch_assoc();
    $current_sort = $max_sort_row['max_sort'] ?? 0;
    
    $inserted_count = 0;
    $updated_count = 0;
    $error_count = 0;
    $ids = [];
    
    foreach ($all_sites as $index => $site) {
        try {
            $site_id = $site['site_id'];
            $ids[] = $site_id;
            
            // 处理网站数据的逻辑（保持原有逻辑）
            $result = processSingleSite($site, $hid, $pricee, $category, $current_sort);
            
            if ($result['success']) {
                if ($result['action'] === 'insert') {
                    $inserted_count++;
                    $current_sort++;
                } else {
                    $updated_count++;
                }
            } else {
                $error_count++;
                echo "❌ 处理网站 {$site_id} 失败: {$result['error']}\n";
            }
            
            // 每处理100个显示进度
            if ($index % 100 == 0 || $index == $total_sites - 1) {
                $progress = round(($index + 1) / $total_sites * 100, 1);
                $current_index = $index + 1;
                echo "进度: {$progress}% ({$current_index}/{$total_sites}) - 新增: {$inserted_count}, 更新: {$updated_count}, 错误: {$error_count}\n";
            }
            
        } catch (Exception $e) {
            $error_count++;
            echo "❌ 处理网站数据异常: " . $e->getMessage() . "\n";
        }
    }
    
    // 下架已删除的项目
    if (!empty($ids)) {
        $ids_str = implode(',', array_map(function($id) use ($DB) {
            return "'" . $DB->escape($id) . "'";
        }, $ids));
        
        $offline_sql = "UPDATE qingka_wangke_class SET status = 0 WHERE docking = '{$hid}' AND noun NOT IN ({$ids_str})";
        $offline_result = $DB->query($offline_sql);
        $offline_count = $DB->affected_rows();
        
        if ($offline_count > 0) {
            echo "📤 下架已删除项目: {$offline_count} 个\n";
        }
    }
    
    return [
        'inserted' => $inserted_count,
        'updated' => $updated_count,
        'errors' => $error_count,
        'offline' => $offline_count ?? 0
    ];
}

/**
 * 处理单个网站数据
 */
function processSingleSite($site, $hid, $pricee, $category, &$current_sort) {
    global $DB;
    
    try {
        $site_id = $site['site_id'];
        $escaped_site_id = $DB->escape($site_id);
        
        // 清理网站名称
        $original_name = $site['site_name'];
        $cleaned_name = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9\s\-_\.]/u', '', $original_name);
        $cleaned_name = trim($cleaned_name);
        
        if (empty($cleaned_name)) {
            $cleaned_name = "网站_" . $site_id;
        }
        
        $name = $DB->escape($cleaned_name);
        $price = $site['price'] * $pricee;
        
        // 提取关键字段信息
        $check_course = isset($site['check_course']) ? $site['check_course'] : '未知';
        $exam_support = isset($site['exam_support']) ? $site['exam_support'] : '未知';
        $site_status = isset($site['status']) ? $site['status'] : '正常';
        
        $check_course_escaped = $DB->escape($check_course);
        $exam_support_escaped = $DB->escape($exam_support);
        $site_status_escaped = $DB->escape($site_status);
        
        // 构建内容信息
        $content_parts = [
            "网站编号: " . $site_id,
            "项目格式: " . ($site['format'] ?? '未知'),
            "项目状态: " . $site_status,
            "项目查课: " . $check_course,
            "项目考试: " . $exam_support,
            "项目说明: " . ($site['description'] ?? '无')
        ];
        
        if (!empty($site['url'])) {
            $content_parts[] = "项目网站: " . $site['url'];
        }
        
        $content = $DB->escape(implode(' | ', $content_parts));
        $now_time = date('Y-m-d H:i:s');
        
        // 检查是否已存在
        $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$escaped_site_id' LIMIT 1");
        
        if (!$rs) {
            // 插入新记录
            $sql = "INSERT INTO qingka_wangke_class
                    (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, check_course, exam_support, site_status, addtime, status, fenlei, uptime)
                    VALUES
                    ('$current_sort', '$name', '$escaped_site_id', '$escaped_site_id', '$price', '$hid', '$hid', '*', '$content', '$check_course_escaped', '$exam_support_escaped', '$site_status_escaped', '$now_time', '1', '$category', '$now_time')";
            
            if ($DB->query($sql)) {
                return ['success' => true, 'action' => 'insert'];
            } else {
                return ['success' => false, 'error' => $DB->error];
            }
        } else {
            // 更新现有记录
            $sql = "UPDATE qingka_wangke_class
                    SET name = '$name',
                        price = '$price',
                        content = '$content',
                        check_course = '$check_course_escaped',
                        exam_support = '$exam_support_escaped',
                        site_status = '$site_status_escaped',
                        uptime = '$now_time',
                        status = '1'
                    WHERE docking = '$hid' AND noun = '$escaped_site_id'";
            
            if ($DB->query($sql)) {
                return ['success' => true, 'action' => 'update'];
            } else {
                return ['success' => false, 'error' => $DB->error];
            }
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 主程序执行
try {
    // 获取Token
    $token = getAuthToken($a);
    
    // 智能获取网站数据
    $sites_result = getAllSitesIntelligent($a, $token);
    
    if (!$sites_result['success']) {
        echo "❌ 获取网站数据失败: {$sites_result['error']}\n";
        jsonReturn(1, $sites_result['error']);
    }
    
    echo "✅ 数据获取成功！\n";
    echo "📊 获取方式: " . ($sites_result['method'] === 'single_request' ? '一次性获取' : '分页获取') . "\n";
    echo "📋 网站总数: " . count($sites_result['data']) . "\n";
    
    // 处理网站数据
    $process_result = processSitesData($sites_result['data'], $a, $pricee, $category);
    
    // 显示最终结果
    echo "\n🎉 同步完成！\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "📈 统计结果:\n";
    echo "   ✅ 新增项目: {$process_result['inserted']} 个\n";
    echo "   🔄 更新项目: {$process_result['updated']} 个\n";
    echo "   📤 下架项目: {$process_result['offline']} 个\n";
    echo "   ❌ 错误项目: {$process_result['errors']} 个\n";
    echo "🕐 完成时间: " . date('Y-m-d H:i:s') . "\n";
    
    // 返回成功结果
    jsonReturn(0, "同步成功", [
        'inserted' => $process_result['inserted'],
        'updated' => $process_result['updated'],
        'offline' => $process_result['offline'],
        'errors' => $process_result['errors'],
        'method' => $sites_result['method']
    ]);
    
} catch (Exception $e) {
    echo "❌ 同步过程发生异常: " . $e->getMessage() . "\n";
    jsonReturn(1, "同步异常: " . $e->getMessage());
}

?>
